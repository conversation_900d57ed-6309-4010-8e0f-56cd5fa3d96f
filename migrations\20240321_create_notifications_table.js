const { pool } = require('../src/config/database');

async function up() {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        // Création de la table notifications
        await client.query(`
            CREATE TABLE IF NOT EXISTS notifications (
                id SERIAL PRIMARY KEY,
                titre VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(50) NOT NULL,
                destinataire_id INTEGER NOT NULL,
                destinataire_type VARCHAR(50) NOT NULL,
                status VARCHAR(20) DEFAULT 'non_lu',
                metadata JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );

            -- Index pour améliorer les performances des requêtes
            CREATE INDEX IF NOT EXISTS idx_notifications_destinataire 
            ON notifications(destinataire_id, destinataire_type);

            CREATE INDEX IF NOT EXISTS idx_notifications_status 
            ON notifications(status);

            CREATE INDEX IF NOT EXISTS idx_notifications_created_at 
            ON notifications(created_at DESC);

            -- Trigger pour mettre à jour updated_at
            CREATE OR REPLACE FUNCTION update_notifications_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            DROP TRIGGER IF EXISTS update_notifications_updated_at_trigger 
            ON notifications;

            CREATE TRIGGER update_notifications_updated_at_trigger
            BEFORE UPDATE ON notifications
            FOR EACH ROW
            EXECUTE FUNCTION update_notifications_updated_at();
        `);

        await client.query('COMMIT');
        console.log('✅ Migration notifications réussie');
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ Erreur lors de la migration notifications:', error);
        throw error;
    } finally {
        client.release();
    }
}

async function down() {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        // Suppression de la table notifications
        await client.query(`
            DROP TRIGGER IF EXISTS update_notifications_updated_at_trigger 
            ON notifications;

            DROP FUNCTION IF EXISTS update_notifications_updated_at();

            DROP TABLE IF EXISTS notifications CASCADE;
        `);

        await client.query('COMMIT');
        console.log('✅ Rollback notifications réussi');
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ Erreur lors du rollback notifications:', error);
        throw error;
    } finally {
        client.release();
    }
}

// Exécuter la migration
up().catch(console.error);

module.exports = { up, down };