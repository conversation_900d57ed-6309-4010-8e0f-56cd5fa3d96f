import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Badge,
  Fab,
} from '@mui/material';
import {
  MedicalServices as MedicalIcon,
  Pets as PetsIcon,
  EventNote as EventIcon,
  LocalHospital as HospitalIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Add as AddIcon,
  CalendarToday as CalendarIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as ChartTooltip, 
  Legend, 
  ResponsiveContainer, 
  PieChart, 
  Pie, 
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

// Couleurs pour les graphiques
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Composant pour afficher une carte de statistique
const StatCard = ({ title, value, icon, color, subtitle }) => {
  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar sx={{ bgcolor: color, mr: 2 }}>
          {icon}
        </Avatar>
        <Box>
          <Typography variant="h4" component="div" fontWeight="bold" color={color}>
            {value}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="caption" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

// Composant pour les actions rapides
const QuickActionDialog = ({ open, onClose, type, onSubmit }) => {
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await onSubmit(formData);
      setFormData({});
      onClose();
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderForm = () => {
    if (type === 'consultation') {
      return (
        <>
          <TextField
            fullWidth
            label="ID Éleveur"
            type="number"
            value={formData.eleveur_id || ''}
            onChange={(e) => setFormData({...formData, eleveur_id: parseInt(e.target.value)})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Date de consultation"
            type="datetime-local"
            value={formData.date_consultation || ''}
            onChange={(e) => setFormData({...formData, date_consultation: e.target.value})}
            margin="normal"
            required
            InputLabelProps={{ shrink: true }}
          />
          <TextField
            fullWidth
            label="Motif"
            multiline
            rows={3}
            value={formData.motif || ''}
            onChange={(e) => setFormData({...formData, motif: e.target.value})}
            margin="normal"
            required
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>Urgence</InputLabel>
            <Select
              value={formData.urgence || false}
              onChange={(e) => setFormData({...formData, urgence: e.target.value})}
            >
              <MenuItem value={false}>Non</MenuItem>
              <MenuItem value={true}>Oui</MenuItem>
            </Select>
          </FormControl>
        </>
      );
    } else if (type === 'prescription') {
      return (
        <>
          <TextField
            fullWidth
            label="ID Éleveur"
            type="number"
            value={formData.eleveur_id || ''}
            onChange={(e) => setFormData({...formData, eleveur_id: parseInt(e.target.value)})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="ID Volaille"
            type="number"
            value={formData.volaille_id || ''}
            onChange={(e) => setFormData({...formData, volaille_id: parseInt(e.target.value)})}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Médicament"
            value={formData.medicament || ''}
            onChange={(e) => setFormData({...formData, medicament: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Dosage"
            value={formData.dosage || ''}
            onChange={(e) => setFormData({...formData, dosage: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Durée du traitement"
            value={formData.duree_traitement || ''}
            onChange={(e) => setFormData({...formData, duree_traitement: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Instructions"
            multiline
            rows={3}
            value={formData.instructions || ''}
            onChange={(e) => setFormData({...formData, instructions: e.target.value})}
            margin="normal"
          />
        </>
      );
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {type === 'consultation' ? 'Programmer une consultation' : 'Créer une prescription'}
      </DialogTitle>
      <DialogContent>
        {renderForm()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Annuler</Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          disabled={loading}
        >
          {loading ? <CircularProgress size={20} /> : 'Créer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const VeterinaireDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quickActionDialog, setQuickActionDialog] = useState({ open: false, type: null });
  const [refreshing, setRefreshing] = useState(false);

  // Configuration des tentatives de reconnexion
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 2000; // 2 secondes

  // Fonction utilitaire pour la gestion des délais
  const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  // Fonction pour récupérer les données avec retry
  const fetchWithRetry = async (url, options, retries = MAX_RETRIES) => {
    try {
      return await axios(url, options);
    } catch (error) {
      if (retries > 0 && error.response?.status === 500) {
        await wait(RETRY_DELAY);
        return fetchWithRetry(url, options, retries - 1);
      }
      throw error;
    }
  };

  // Fonction pour récupérer les données du dashboard
  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);
      const token = localStorage.getItem('token');
      const headers = { Authorization: `Bearer ${token}` };
      
      const [dashboardResponse, notificationsResponse] = await Promise.all([
        fetchWithRetry('/api/veterinaire/dashboard', { headers, method: 'GET' }),
        fetchWithRetry('/api/veterinaire/notifications', { headers, method: 'GET' })
      ]);

      setDashboardData(dashboardResponse.data.data);
      setNotifications(notificationsResponse.data.data.notifications);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la récupération des données:', err);
      let errorMessage = 'Une erreur est survenue lors du chargement des données.';
      
      if (err.response) {
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expirée. Veuillez vous reconnecter.';
            break;
          case 403:
            errorMessage = 'Vous n\'avez pas les permissions nécessaires.';
            break;
          case 404:
            errorMessage = 'Les données demandées sont introuvables.';
            break;
          case 500:
            errorMessage = 'Erreur serveur. Veuillez réessayer plus tard.';
            break;
          default:
            errorMessage = `Erreur ${err.response.status}: ${err.response.data.message || 'Erreur inconnue'}`;
        }
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fonction pour les actions rapides
  const handleQuickAction = async (type, data) => {
    try {
      const token = localStorage.getItem('token');
      const endpoint = type === 'consultation' 
        ? '/api/veterinaire/consultations/quick'
        : '/api/veterinaire/prescriptions/quick';

      await axios.post(endpoint, data, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Rafraîchir les données
      fetchDashboardData();
    } catch (error) {
      console.error('Erreur lors de l\'action rapide:', error);
      throw error;
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Rafraîchir les données toutes les 5 minutes
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={fetchDashboardData}>
          Réessayer
        </Button>
      </Container>
    );
  }

  const { stats, consultationsAVenir, prescriptionsRecentes, consultationsHistorique, graphiques, alertesSante } = dashboardData;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* En-tête avec actions rapides */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard Vétérinaire
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Badge badgeContent={notifications.length} color="error">
            <IconButton color="primary">
              <NotificationsIcon />
            </IconButton>
          </Badge>
          <Button
            variant="outlined"
            startIcon={<CalendarIcon />}
            onClick={() => setQuickActionDialog({ open: true, type: 'consultation' })}
          >
            Nouvelle consultation
          </Button>
          <Button
            variant="contained"
            startIcon={<MedicalIcon />}
            onClick={() => setQuickActionDialog({ open: true, type: 'prescription' })}
          >
            Nouvelle prescription
          </Button>
        </Box>
      </Box>

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Prescriptions"
            value={stats.totalPrescriptions}
            subtitle={`+${stats.prescriptionsMois} ce mois`}
            icon={<MedicalIcon />}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Consultations"
            value={stats.totalConsultations}
            subtitle={`+${stats.consultationsMois} ce mois`}
            icon={<EventIcon />}
            color="#2e7d32"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Éleveurs Suivis"
            value={stats.eleveursSuivis}
            subtitle="Clients actifs"
            icon={<PeopleIcon />}
            color="#ed6c02"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Prochaine Semaine"
            value={stats.consultationsSemaineProchaine}
            subtitle="Consultations programmées"
            icon={<ScheduleIcon />}
            color="#9c27b0"
          />
        </Grid>
      </Grid>

      {/* Alertes santé */}
      {alertesSante && alertesSante.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
                Alertes Santé Récentes
              </Typography>
              <List>
                {alertesSante.slice(0, 5).map((alerte) => (
                  <ListItem key={alerte.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: alerte.priorite === 'critique' ? 'error.main' : 'warning.main' }}>
                        <WarningIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={alerte.titre}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {alerte.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Éleveur: {alerte.eleveur_nom} {alerte.eleveur_prenom} - {new Date(alerte.date_declenchement).toLocaleDateString()}
                          </Typography>
                        </Box>
                      }
                    />
                    <Chip 
                      label={alerte.priorite} 
                      color={alerte.priorite === 'critique' ? 'error' : 'warning'}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>
        </Grid>
      )}

      <Grid container spacing={3}>
        {/* Consultations à venir */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <CalendarIcon sx={{ mr: 1 }} />
              Consultations à Venir
            </Typography>
            <List>
              {consultationsAVenir.map((consultation) => (
                <ListItem key={consultation.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: consultation.urgence ? 'error.main' : 'primary.main' }}>
                      <EventIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={`${consultation.eleveur_nom} ${consultation.eleveur_prenom}`}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {consultation.motif}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(consultation.date_consultation).toLocaleString()}
                        </Typography>
                        {consultation.urgence && (
                          <Chip label="URGENT" color="error" size="small" sx={{ ml: 1 }} />
                        )}
                      </Box>
                    }
                  />
                  <IconButton size="small">
                    <VisibilityIcon />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Prescriptions récentes */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <MedicalIcon sx={{ mr: 1 }} />
              Prescriptions Récentes
            </Typography>
            <List>
              {prescriptionsRecentes.map((prescription) => (
                <ListItem key={prescription.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <MedicalIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={prescription.medicament}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {prescription.eleveur_nom} {prescription.eleveur_prenom}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Dosage: {prescription.dosage}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(prescription.created_at).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                  />
                  <Chip 
                    label={prescription.status} 
                    color={prescription.status === 'active' ? 'success' : 'default'}
                    size="small"
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Graphiques */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Consultations par Mois
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <AreaChart data={graphiques.consultationsParMois}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="mois" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { month: 'short' })}
                />
                <YAxis />
                <ChartTooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="nombre_consultations" 
                  stroke="#1976d2" 
                  fill="#1976d2" 
                  fillOpacity={0.3}
                  name="Consultations"
                />
                <Area 
                  type="monotone" 
                  dataKey="consultations_terminees" 
                  stroke="#2e7d32" 
                  fill="#2e7d32" 
                  fillOpacity={0.3}
                  name="Terminées"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Types de consultations */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Types de Consultations
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <PieChart>
                <Pie
                  data={graphiques.typesConsultations}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ motif, pourcentage }) => `${motif}: ${pourcentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="nombre"
                >
                  {graphiques.typesConsultations.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <ChartTooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Bouton de rafraîchissement flottant */}
      <Fab
        color="primary"
        aria-label="refresh"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={fetchDashboardData}
        disabled={refreshing}
      >
        {refreshing ? <CircularProgress size={24} /> : <TrendingUpIcon />}
      </Fab>

      {/* Dialog pour les actions rapides */}
      <QuickActionDialog
        open={quickActionDialog.open}
        type={quickActionDialog.type}
        onClose={() => setQuickActionDialog({ open: false, type: null })}
        onSubmit={(data) => handleQuickAction(quickActionDialog.type, data)}
      />
    </Container>
  );
};

export default VeterinaireDashboard;

