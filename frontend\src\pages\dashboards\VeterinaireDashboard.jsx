import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Badge,
  Fab,
  Tabs,
  Tab,
  LinearProgress,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  MedicalServices as MedicalIcon,
  Pets as PetsIcon,
  EventNote as EventIcon,
  LocalHospital as HospitalIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Add as AddIcon,
  CalendarToday as CalendarIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/VeterinaireDashboard.css';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

// Couleurs pour les graphiques
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Composant pour afficher une carte de statistique
const StatCard = ({ title, value, icon, color, subtitle }) => {
  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar sx={{ bgcolor: color, mr: 2 }}>
          {icon}
        </Avatar>
        <Box>
          <Typography variant="h4" component="div" fontWeight="bold" color={color}>
            {value}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="caption" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

// Composant pour les actions rapides
const QuickActionDialog = ({ open, onClose, type, onSubmit }) => {
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await onSubmit(formData);
      setFormData({});
      onClose();
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderForm = () => {
    if (type === 'consultation') {
      return (
        <>
          <TextField
            fullWidth
            label="ID Éleveur"
            type="number"
            value={formData.eleveur_id || ''}
            onChange={(e) => setFormData({...formData, eleveur_id: parseInt(e.target.value)})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Date de consultation"
            type="datetime-local"
            value={formData.date_consultation || ''}
            onChange={(e) => setFormData({...formData, date_consultation: e.target.value})}
            margin="normal"
            required
            InputLabelProps={{ shrink: true }}
          />
          <TextField
            fullWidth
            label="Motif"
            multiline
            rows={3}
            value={formData.motif || ''}
            onChange={(e) => setFormData({...formData, motif: e.target.value})}
            margin="normal"
            required
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>Urgence</InputLabel>
            <Select
              value={formData.urgence || false}
              onChange={(e) => setFormData({...formData, urgence: e.target.value})}
            >
              <MenuItem value={false}>Non</MenuItem>
              <MenuItem value={true}>Oui</MenuItem>
            </Select>
          </FormControl>
        </>
      );
    } else if (type === 'prescription') {
      return (
        <>
          <TextField
            fullWidth
            label="ID Éleveur"
            type="number"
            value={formData.eleveur_id || ''}
            onChange={(e) => setFormData({...formData, eleveur_id: parseInt(e.target.value)})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="ID Volaille"
            type="number"
            value={formData.volaille_id || ''}
            onChange={(e) => setFormData({...formData, volaille_id: parseInt(e.target.value)})}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Médicament"
            value={formData.medicament || ''}
            onChange={(e) => setFormData({...formData, medicament: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Dosage"
            value={formData.dosage || ''}
            onChange={(e) => setFormData({...formData, dosage: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Durée du traitement"
            value={formData.duree_traitement || ''}
            onChange={(e) => setFormData({...formData, duree_traitement: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Instructions"
            multiline
            rows={3}
            value={formData.instructions || ''}
            onChange={(e) => setFormData({...formData, instructions: e.target.value})}
            margin="normal"
          />
        </>
      );
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {type === 'consultation' ? 'Programmer une consultation' : 'Créer une prescription'}
      </DialogTitle>
      <DialogContent>
        {renderForm()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Annuler</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
        >
          {loading ? <CircularProgress size={20} /> : 'Créer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const VeterinaireDashboard = () => {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Get current section from URL params
  const searchParams = new URLSearchParams(location.search);
  const currentSection = searchParams.get('section') || 'dashboard';

  // State management
  const [dashboardData, setDashboardData] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quickActionDialog, setQuickActionDialog] = useState({ open: false, type: null });
  const [refreshing, setRefreshing] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedPetType, setSelectedPetType] = useState('all');
  const [expandedPatient, setExpandedPatient] = useState(null);

  // Settings states
  const [profileSettings, setProfileSettings] = useState({
    firstName: user?.first_name || '',
    lastName: user?.last_name || '',
    email: user?.email || '',
    phone: '',
    specialization: '',
    clinicName: '',
    address: ''
  });

  const [smtpSettings, setSmtpSettings] = useState({
    host: '',
    port: 587,
    username: '',
    password: '',
    encryption: 'tls'
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordExpiry: 90
  });

  // Configuration des tentatives de reconnexion
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 2000; // 2 secondes

  // Enhanced mock data for veterinary dashboard
  const mockEnhancedData = {
    urgentAlerts: [
      {
        id: 1,
        type: 'vaccination',
        priority: 'high',
        message: 'Vaccination H5N1 due pour Ferme Dupont',
        patient: 'Poules pondeuses - Lot A',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        action: 'Programmer vaccination'
      },
      {
        id: 2,
        type: 'lab_results',
        priority: 'critical',
        message: 'Résultats de laboratoire critiques',
        patient: 'Dindes - Ferme Martin',
        dueDate: new Date(),
        action: 'Consulter résultats'
      },
      {
        id: 3,
        type: 'medication',
        priority: 'medium',
        message: 'Stock antibiotique faible',
        patient: 'Inventaire général',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        action: 'Réapprovisionner'
      }
    ],
    activePatients: [
      {
        id: 1,
        name: 'Ferme Dupont - Lot A',
        species: 'Poules pondeuses',
        count: 250,
        status: 'stable',
        lastVisit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        nextVisit: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        condition: 'Contrôle de routine',
        owner: 'Jean Dupont',
        phone: '**********'
      },
      {
        id: 2,
        name: 'Ferme Martin - Dindes',
        species: 'Dindes',
        count: 150,
        status: 'urgent',
        lastVisit: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        nextVisit: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
        condition: 'Infection respiratoire',
        owner: 'Marie Martin',
        phone: '**********'
      },
      {
        id: 3,
        name: 'Élevage Durand - Canards',
        species: 'Canards',
        count: 80,
        status: 'post-op',
        lastVisit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        nextVisit: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        condition: 'Suivi post-opératoire',
        owner: 'Pierre Durand',
        phone: '**********'
      }
    ],
    realTimeAppointments: {
      current: {
        id: 1,
        time: '14:30',
        patient: 'Ferme Dubois - Cailles',
        owner: 'Sophie Dubois',
        type: 'Consultation urgente',
        duration: 45,
        status: 'in_progress'
      },
      next: [
        {
          id: 2,
          time: '15:30',
          patient: 'Ferme Leroy - Poules',
          owner: 'Paul Leroy',
          type: 'Vaccination',
          duration: 30
        },
        {
          id: 3,
          time: '16:15',
          patient: 'Élevage Bernard - Dindes',
          owner: 'Claire Bernard',
          type: 'Contrôle de routine',
          duration: 60
        },
        {
          id: 4,
          time: '17:30',
          patient: 'Ferme Rousseau - Oies',
          owner: 'Michel Rousseau',
          type: 'Consultation',
          duration: 45
        }
      ]
    },
    medicationInventory: [
      {
        id: 1,
        name: 'Amoxicilline 500mg',
        category: 'Antibiotique',
        currentStock: 15,
        minStock: 20,
        maxStock: 100,
        status: 'low',
        expiryDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        supplier: 'VetPharma'
      },
      {
        id: 2,
        name: 'Vaccin H5N1',
        category: 'Vaccin',
        currentStock: 45,
        minStock: 30,
        maxStock: 80,
        status: 'ok',
        expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        supplier: 'BioVet'
      },
      {
        id: 3,
        name: 'Anti-inflammatoire',
        category: 'Anti-inflammatoire',
        currentStock: 5,
        minStock: 15,
        maxStock: 50,
        status: 'critical',
        expiryDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
        supplier: 'MediVet'
      }
    ],
    consultationStats: {
      monthly: [
        { month: 'Jan', consultations: 45, emergencies: 8, vaccinations: 12, surgeries: 3 },
        { month: 'Fév', consultations: 52, emergencies: 6, vaccinations: 15, surgeries: 4 },
        { month: 'Mar', consultations: 48, emergencies: 10, vaccinations: 18, surgeries: 2 },
        { month: 'Avr', consultations: 55, emergencies: 7, vaccinations: 20, surgeries: 5 },
        { month: 'Mai', consultations: 60, emergencies: 9, vaccinations: 22, surgeries: 6 },
        { month: 'Juin', consultations: 58, emergencies: 11, vaccinations: 19, surgeries: 4 }
      ],
      types: [
        { name: 'Urgences', value: 25, color: '#ff4444' },
        { name: 'Vaccinations', value: 35, color: '#00C49F' },
        { name: 'Chirurgies', value: 15, color: '#FFBB28' },
        { name: 'Contrôles', value: 25, color: '#0088FE' }
      ]
    }
  };

  // Fonction utilitaire pour la gestion des délais
  const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  // Fonction pour récupérer les données avec retry
  const fetchWithRetry = async (url, options, retries = MAX_RETRIES) => {
    try {
      return await axios(url, options);
    } catch (error) {
      if (retries > 0 && error.response?.status === 500) {
        await wait(RETRY_DELAY);
        return fetchWithRetry(url, options, retries - 1);
      }
      throw error;
    }
  };

  // Fonction pour récupérer les données du dashboard (utilise des données mock)
  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);
      setLoading(true);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Use comprehensive mock data instead of API calls
      const mockDashboardData = {
        stats: {
          totalPrescriptions: 156,
          prescriptionsMois: 23,
          totalConsultations: 342,
          consultationsMois: 45,
          eleveursSuivis: 78,
          consultationsSemaineProchaine: 12,
          prescriptionsActives: 34,
          satisfactionMoyenne: 4.6
        },
        consultationsAVenir: [
          {
            id: 1,
            eleveur_nom: 'Dupont',
            eleveur_prenom: 'Jean',
            motif: 'Vaccination H5N1 - Lot de poules pondeuses',
            date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
            urgence: false,
            espece: 'Poules',
            nombre_animaux: 250
          },
          {
            id: 2,
            eleveur_nom: 'Martin',
            eleveur_prenom: 'Marie',
            motif: 'Consultation urgente - Infection respiratoire',
            date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
            urgence: true,
            espece: 'Dindes',
            nombre_animaux: 150
          },
          {
            id: 3,
            eleveur_nom: 'Durand',
            eleveur_prenom: 'Pierre',
            motif: 'Contrôle post-opératoire',
            date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
            urgence: false,
            espece: 'Canards',
            nombre_animaux: 80
          }
        ],
        prescriptionsRecentes: [
          {
            id: 1,
            eleveur_nom: 'Dubois',
            eleveur_prenom: 'Sophie',
            diagnostic: 'Infection bactérienne',
            medicaments: 'Amoxicilline 500mg',
            statut: 'en_cours',
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 2,
            eleveur_nom: 'Leroy',
            eleveur_prenom: 'Paul',
            diagnostic: 'Prévention parasitaire',
            medicaments: 'Vermifuge',
            statut: 'terminee',
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
          }
        ],
        consultationsHistorique: [
          {
            id: 1,
            eleveur_nom: 'Bernard',
            eleveur_prenom: 'Claire',
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            diagnostic: 'Vaccination de routine',
            statut: 'terminee',
            type: 'Vaccination'
          },
          {
            id: 2,
            eleveur_nom: 'Rousseau',
            eleveur_prenom: 'Michel',
            date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
            diagnostic: 'Contrôle sanitaire',
            statut: 'terminee',
            type: 'Contrôle'
          }
        ],
        graphiques: {
          consultationsParMois: [
            { mois: '2024-01-01', nombre_consultations: 45, consultations_terminees: 42 },
            { mois: '2024-02-01', nombre_consultations: 52, consultations_terminees: 48 },
            { mois: '2024-03-01', nombre_consultations: 48, consultations_terminees: 45 },
            { mois: '2024-04-01', nombre_consultations: 55, consultations_terminees: 52 },
            { mois: '2024-05-01', nombre_consultations: 60, consultations_terminees: 57 },
            { mois: '2024-06-01', nombre_consultations: 58, consultations_terminees: 55 }
          ],
          typesConsultations: [
            { motif: 'Urgences', nombre: 25, pourcentage: 25 },
            { motif: 'Vaccinations', nombre: 35, pourcentage: 35 },
            { motif: 'Chirurgies', nombre: 15, pourcentage: 15 },
            { motif: 'Contrôles', nombre: 25, pourcentage: 25 }
          ]
        },
        alertesSante: [
          {
            id: 1,
            type: 'vaccination',
            message: 'Vaccination H5N1 due pour Ferme Dupont',
            priorite: 'haute',
            date_echeance: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
          }
        ]
      };

      const mockNotifications = [
        {
          id: 1,
          type: 'urgent',
          message: 'Résultats de laboratoire critiques disponibles',
          date: new Date().toISOString(),
          lu: false
        },
        {
          id: 2,
          type: 'info',
          message: 'Nouveau rendez-vous programmé pour demain',
          date: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          lu: false
        }
      ];

      setDashboardData(mockDashboardData);
      setNotifications(mockNotifications);
      setError(null);
      console.log('✅ Dashboard data loaded successfully with mock data');
    } catch (err) {
      console.error('Erreur lors de la récupération des données:', err);
      setError('Une erreur est survenue lors du chargement des données.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fonction pour les actions rapides
  const handleQuickAction = async (type, data) => {
    try {
      const token = localStorage.getItem('token');
      const endpoint = type === 'consultation'
        ? '/api/veterinaire/consultations/quick'
        : '/api/veterinaire/prescriptions/quick';

      await axios.post(endpoint, data, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Rafraîchir les données
      fetchDashboardData();
    } catch (error) {
      console.error('Erreur lors de l\'action rapide:', error);
      throw error;
    }
  };

  useEffect(() => {
    fetchDashboardData();

    // Rafraîchir les données toutes les 5 minutes
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={fetchDashboardData}>
          Réessayer
        </Button>
      </Container>
    );
  }

  const { stats, consultationsAVenir, prescriptionsRecentes, consultationsHistorique, graphiques, alertesSante } = dashboardData || {};

  // Section rendering functions
  const renderDashboardSection = () => (
    <Container maxWidth="lg" className="veterinary-dashboard" sx={{ mt: 4, mb: 4 }}>
      {/* En-tête avec actions rapides */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard Vétérinaire
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Badge badgeContent={notifications.length} color="error">
            <IconButton color="primary">
              <NotificationsIcon />
            </IconButton>
          </Badge>
          <Button
            variant="outlined"
            startIcon={<CalendarIcon />}
            onClick={() => setQuickActionDialog({ open: true, type: 'consultation' })}
          >
            Nouvelle consultation
          </Button>
          <Button
            variant="contained"
            startIcon={<MedicalIcon />}
            onClick={() => setQuickActionDialog({ open: true, type: 'prescription' })}
          >
            Nouvelle prescription
          </Button>
        </Box>
      </Box>

      {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Prescriptions"
            value={stats.totalPrescriptions}
            subtitle={`+${stats.prescriptionsMois} ce mois`}
            icon={<MedicalIcon />}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Consultations"
            value={stats.totalConsultations}
            subtitle={`+${stats.consultationsMois} ce mois`}
            icon={<EventIcon />}
            color="#2e7d32"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Éleveurs Suivis"
            value={stats.eleveursSuivis}
            subtitle="Clients actifs"
            icon={<PeopleIcon />}
            color="#ed6c02"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Prochaine Semaine"
            value={stats.consultationsSemaineProchaine}
            subtitle="Consultations programmées"
            icon={<ScheduleIcon />}
            color="#9c27b0"
          />
        </Grid>
      </Grid>

      {/* Alertes santé */}
      {alertesSante && alertesSante.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
                Alertes Santé Récentes
              </Typography>
              <List>
                {alertesSante.slice(0, 5).map((alerte) => (
                  <ListItem key={alerte.id} divider>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: alerte.priorite === 'critique' ? 'error.main' : 'warning.main' }}>
                        <WarningIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={alerte.titre}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {alerte.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Éleveur: {alerte.eleveur_nom} {alerte.eleveur_prenom} - {new Date(alerte.date_declenchement).toLocaleDateString()}
                          </Typography>
                        </Box>
                      }
                    />
                    <Chip
                      label={alerte.priorite}
                      color={alerte.priorite === 'critique' ? 'error' : 'warning'}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>
        </Grid>
      )}

      <Grid container spacing={3}>
        {/* Consultations à venir */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <CalendarIcon sx={{ mr: 1 }} />
              Consultations à Venir
            </Typography>
            <List>
              {consultationsAVenir.map((consultation) => (
                <ListItem key={consultation.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: consultation.urgence ? 'error.main' : 'primary.main' }}>
                      <EventIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={`${consultation.eleveur_nom} ${consultation.eleveur_prenom}`}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {consultation.motif}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(consultation.date_consultation).toLocaleString()}
                        </Typography>
                        {consultation.urgence && (
                          <Chip label="URGENT" color="error" size="small" sx={{ ml: 1 }} />
                        )}
                      </Box>
                    }
                  />
                  <IconButton size="small">
                    <VisibilityIcon />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Prescriptions récentes */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <MedicalIcon sx={{ mr: 1 }} />
              Prescriptions Récentes
            </Typography>
            <List>
              {prescriptionsRecentes.map((prescription) => (
                <ListItem key={prescription.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <MedicalIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={prescription.medicament}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {prescription.eleveur_nom} {prescription.eleveur_prenom}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Dosage: {prescription.dosage}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(prescription.created_at).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                  />
                  <Chip
                    label={prescription.status}
                    color={prescription.status === 'active' ? 'success' : 'default'}
                    size="small"
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Graphiques */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Consultations par Mois
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <AreaChart data={graphiques.consultationsParMois}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="mois"
                  tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { month: 'short' })}
                />
                <YAxis />
                <ChartTooltip
                  labelFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="nombre_consultations"
                  stroke="#1976d2"
                  fill="#1976d2"
                  fillOpacity={0.3}
                  name="Consultations"
                />
                <Area
                  type="monotone"
                  dataKey="consultations_terminees"
                  stroke="#2e7d32"
                  fill="#2e7d32"
                  fillOpacity={0.3}
                  name="Terminées"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Types de consultations */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Types de Consultations
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <PieChart>
                <Pie
                  data={graphiques.typesConsultations}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ motif, pourcentage }) => `${motif}: ${pourcentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="nombre"
                >
                  {graphiques.typesConsultations.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <ChartTooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Enhanced Veterinary Modules */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Urgent Alerts Panel */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} className="vet-card urgent-alert-card custom-scrollbar" sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: '#ff4444' }}>
              <WarningIcon sx={{ mr: 1 }} />
              🔴 Alertes Urgentes
            </Typography>
            <List>
              {mockEnhancedData.urgentAlerts.map((alert) => (
                <ListItem key={alert.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{
                      bgcolor: alert.priority === 'critical' ? '#ff4444' :
                               alert.priority === 'high' ? '#ff8800' : '#ffbb00'
                    }}>
                      <WarningIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={alert.message}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {alert.patient}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Échéance: {alert.dueDate.toLocaleDateString('fr-FR')}
                        </Typography>
                      </Box>
                    }
                  />
                  <Button size="small" variant="outlined" color="error">
                    {alert.action}
                  </Button>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Active Patients Overview */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} className="vet-card patient-card custom-scrollbar" sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <PetsIcon sx={{ mr: 1, color: '#2e7d32' }} />
              🐕 Patients Actifs
            </Typography>
            <List>
              {mockEnhancedData.activePatients.map((patient) => (
                <Accordion key={patient.id} expanded={expandedPatient === patient.id}
                          onChange={() => setExpandedPatient(expandedPatient === patient.id ? null : patient.id)}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <Chip
                        label={patient.status}
                        color={patient.status === 'urgent' ? 'error' :
                               patient.status === 'post-op' ? 'warning' : 'success'}
                        size="small"
                        sx={{ mr: 2 }}
                      />
                      <Typography variant="subtitle1">{patient.name}</Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2"><strong>Espèce:</strong> {patient.species}</Typography>
                        <Typography variant="body2"><strong>Nombre:</strong> {patient.count}</Typography>
                        <Typography variant="body2"><strong>Propriétaire:</strong> {patient.owner}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2"><strong>Condition:</strong> {patient.condition}</Typography>
                        <Typography variant="body2"><strong>Dernière visite:</strong> {patient.lastVisit.toLocaleDateString('fr-FR')}</Typography>
                        <Typography variant="body2"><strong>Prochaine visite:</strong> {patient.nextVisit.toLocaleDateString('fr-FR')}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                          <Button size="small" variant="outlined" startIcon={<PhoneIcon />}>
                            Appeler
                          </Button>
                          <Button size="small" variant="outlined" startIcon={<EventIcon />}>
                            Programmer
                          </Button>
                          <Button size="small" variant="outlined" startIcon={<MedicalIcon />}>
                            Prescrire
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Real-Time Appointment Tracker */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} className="vet-card appointment-tracker custom-scrollbar" sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: '#1976d2' }}>
              <ScheduleIcon sx={{ mr: 1 }} />
              📅 Rendez-vous en Temps Réel
            </Typography>

            {/* Current Appointment */}
            <Box className="current-appointment" sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                🔄 En cours - {mockEnhancedData.realTimeAppointments.current.time}
              </Typography>
              <Typography variant="body2">{mockEnhancedData.realTimeAppointments.current.patient}</Typography>
              <Typography variant="body2">{mockEnhancedData.realTimeAppointments.current.type}</Typography>
              <LinearProgress
                variant="determinate"
                value={65}
                sx={{ mt: 1, height: 8, borderRadius: 4 }}
              />
              <Typography variant="caption">Durée: {mockEnhancedData.realTimeAppointments.current.duration} min</Typography>
            </Box>

            {/* Next Appointments */}
            <Typography variant="subtitle2" sx={{ mb: 2 }}>Prochains rendez-vous:</Typography>
            <List dense>
              {mockEnhancedData.realTimeAppointments.next.map((appointment) => (
                <ListItem key={appointment.id} divider>
                  <ListItemText
                    primary={`${appointment.time} - ${appointment.patient}`}
                    secondary={`${appointment.type} (${appointment.duration} min)`}
                  />
                  <IconButton size="small" color="primary">
                    <VisibilityIcon />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Medication Inventory */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <InventoryIcon sx={{ mr: 1, color: '#ed6c02' }} />
              💊 Inventaire Médicaments
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Médicament</TableCell>
                    <TableCell>Stock</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mockEnhancedData.medicationInventory.map((med) => (
                    <TableRow key={med.id}>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {med.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {med.category}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body2">{med.currentStock}/{med.maxStock}</Typography>
                          <LinearProgress
                            variant="determinate"
                            value={(med.currentStock / med.maxStock) * 100}
                            sx={{ ml: 1, width: 50, height: 6 }}
                            color={med.status === 'critical' ? 'error' : med.status === 'low' ? 'warning' : 'success'}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={med.status === 'critical' ? 'Critique' : med.status === 'low' ? 'Faible' : 'OK'}
                          color={med.status === 'critical' ? 'error' : med.status === 'low' ? 'warning' : 'success'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {med.status !== 'ok' && (
                          <Button size="small" variant="outlined" color="primary">
                            Commander
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Enhanced Interactive Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Interactive Monthly Bar Chart */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 3, height: '400px' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">📊 Consultations Mensuelles Interactives</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <FormControl size="small">
                  <Select value={selectedYear} onChange={(e) => setSelectedYear(e.target.value)}>
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - 2 + i;
                      return (
                        <MenuItem key={year} value={year}>
                          {year}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
                <FormControl size="small">
                  <Select value={selectedPetType} onChange={(e) => setSelectedPetType(e.target.value)}>
                    <MenuItem value="all">Tous</MenuItem>
                    <MenuItem value="poules">Poules</MenuItem>
                    <MenuItem value="dindes">Dindes</MenuItem>
                    <MenuItem value="canards">Canards</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
            <ResponsiveContainer width="100%" height="85%">
              <BarChart data={mockEnhancedData.consultationStats.monthly}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <ChartTooltip />
                <Legend />
                <Bar dataKey="consultations" fill="#1976d2" name="Total" />
                <Bar dataKey="emergencies" fill="#ff4444" name="Urgences" />
                <Bar dataKey="vaccinations" fill="#00C49F" name="Vaccinations" />
                <Bar dataKey="surgeries" fill="#FFBB28" name="Chirurgies" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Color-coded Pie Chart */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>🎯 Types de Consultations</Typography>
            <ResponsiveContainer width="100%" height="85%">
              <PieChart>
                <Pie
                  data={mockEnhancedData.consultationStats.types}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockEnhancedData.consultationStats.types.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ChartTooltip />
              </PieChart>
            </ResponsiveContainer>

            {/* Quick Action Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
              <Button variant="contained" startIcon={<AddIcon />} className="quick-action-btn quick-action-primary" color="primary">
                Nouveau Dossier Patient
              </Button>
              <Button variant="contained" startIcon={<MedicalIcon />} className="quick-action-btn quick-action-success" color="success">
                Générateur Prescription
              </Button>
              <Button variant="contained" startIcon={<AssignmentIcon />} className="quick-action-btn quick-action-primary" color="info">
                Demande Labo
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Bouton de rafraîchissement flottant */}
      <Fab
        color="primary"
        aria-label="refresh"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={fetchDashboardData}
        disabled={refreshing}
      >
        {refreshing ? <CircularProgress size={24} /> : <TrendingUpIcon />}
      </Fab>

      {/* Dialog pour les actions rapides */}
      <QuickActionDialog
        open={quickActionDialog.open}
        type={quickActionDialog.type}
        onClose={() => setQuickActionDialog({ open: false, type: null })}
        onSubmit={(data) => handleQuickAction(quickActionDialog.type, data)}
      />
    </Container>
  );

  // Other section rendering functions
  const renderProfileSection = () => (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        👤 Profil Vétérinaire
      </Typography>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Prénom"
              value={profileSettings.firstName}
              onChange={(e) => setProfileSettings({...profileSettings, firstName: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Nom"
              value={profileSettings.lastName}
              onChange={(e) => setProfileSettings({...profileSettings, lastName: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Email"
              value={profileSettings.email}
              onChange={(e) => setProfileSettings({...profileSettings, email: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Téléphone"
              value={profileSettings.phone}
              onChange={(e) => setProfileSettings({...profileSettings, phone: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Spécialisation"
              value={profileSettings.specialization}
              onChange={(e) => setProfileSettings({...profileSettings, specialization: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Nom de la clinique"
              value={profileSettings.clinicName}
              onChange={(e) => setProfileSettings({...profileSettings, clinicName: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Adresse"
              multiline
              rows={3}
              value={profileSettings.address}
              onChange={(e) => setProfileSettings({...profileSettings, address: e.target.value})}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <Button variant="contained" startIcon={<SaveIcon />}>
                Sauvegarder
              </Button>
              <Button variant="outlined" startIcon={<CancelIcon />}>
                Annuler
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );

  const renderPrescriptionsSection = () => (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        💊 Gestion des Prescriptions
      </Typography>
      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Prescriptions récentes
        </Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Patient</TableCell>
                <TableCell>Diagnostic</TableCell>
                <TableCell>Médicaments</TableCell>
                <TableCell>Statut</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {prescriptionsRecentes?.map((prescription) => (
                <TableRow key={prescription.id}>
                  <TableCell>{new Date(prescription.created_at).toLocaleDateString('fr-FR')}</TableCell>
                  <TableCell>{prescription.eleveur_nom} {prescription.eleveur_prenom}</TableCell>
                  <TableCell>{prescription.diagnostic}</TableCell>
                  <TableCell>{prescription.medicaments || 'Non spécifié'}</TableCell>
                  <TableCell>
                    <Chip
                      label={prescription.statut}
                      color={prescription.statut === 'en_cours' ? 'warning' : 'success'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary">
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton size="small" color="secondary">
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )) || []}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Container>
  );

  const renderConsultationsSection = () => (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        🩺 Gestion des Consultations
      </Typography>
      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Consultations à venir
        </Typography>
        <List>
          {consultationsAVenir?.map((consultation) => (
            <ListItem key={consultation.id} divider>
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: consultation.urgence ? 'error.main' : 'primary.main' }}>
                  <EventIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={`${consultation.eleveur_nom} ${consultation.eleveur_prenom}`}
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      {consultation.motif || consultation.symptomes}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(consultation.date).toLocaleString('fr-FR')}
                    </Typography>
                  </Box>
                }
              />
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button size="small" variant="outlined">
                  Modifier
                </Button>
                <Button size="small" variant="contained">
                  Commencer
                </Button>
              </Box>
            </ListItem>
          )) || []}
        </List>
      </Paper>
    </Container>
  );

  const renderHistoriqueSection = () => (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        📋 Historique des Consultations
      </Typography>
      <Paper elevation={3} sx={{ p: 3 }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Patient</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Diagnostic</TableCell>
                <TableCell>Statut</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {consultationsHistorique?.map((consultation) => (
                <TableRow key={consultation.id}>
                  <TableCell>{new Date(consultation.date).toLocaleDateString('fr-FR')}</TableCell>
                  <TableCell>{consultation.eleveur_nom} {consultation.eleveur_prenom}</TableCell>
                  <TableCell>{consultation.type || 'Consultation'}</TableCell>
                  <TableCell>{consultation.diagnostic || consultation.symptomes}</TableCell>
                  <TableCell>
                    <Chip
                      label={consultation.statut}
                      color={consultation.statut === 'terminee' ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary">
                      <VisibilityIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )) || []}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Container>
  );

  const renderSettingsSection = () => {
    const settingsContent = {
      'settings-general': (
        <Paper elevation={3} sx={{ p: 4 }}>
          <Typography variant="h6" gutterBottom>
            ⚙️ Paramètres Généraux
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 2 }}>
                Configurez vos préférences personnelles pour votre pratique vétérinaire.
              </Alert>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.twoFactorEnabled}
                    onChange={(e) => setSecuritySettings({...securitySettings, twoFactorEnabled: e.target.checked})}
                  />
                }
                label="Authentification à deux facteurs"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Délai d'expiration de session (minutes)"
                type="number"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: parseInt(e.target.value)})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Expiration du mot de passe (jours)"
                type="number"
                value={securitySettings.passwordExpiry}
                onChange={(e) => setSecuritySettings({...securitySettings, passwordExpiry: parseInt(e.target.value)})}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Notifications
              </Typography>
              <FormControlLabel
                control={<Switch defaultChecked />}
                label="Alertes de vaccination"
              />
              <FormControlLabel
                control={<Switch defaultChecked />}
                label="Rappels de rendez-vous"
              />
              <FormControlLabel
                control={<Switch />}
                label="Notifications par email"
              />
            </Grid>
          </Grid>
        </Paper>
      ),
      'settings-security': (
        <Paper elevation={3} sx={{ p: 4 }}>
          <Typography variant="h6" gutterBottom>
            🔒 Paramètres de Sécurité
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 2 }}>
                Ces paramètres affectent la sécurité de votre compte vétérinaire.
              </Alert>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.twoFactorEnabled}
                    onChange={(e) => setSecuritySettings({...securitySettings, twoFactorEnabled: e.target.checked})}
                  />
                }
                label="Authentification à deux facteurs"
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Gestion du mot de passe
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Button variant="outlined" color="warning">
                  Changer le mot de passe
                </Button>
                <Button variant="outlined" color="info">
                  Historique des connexions
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Sessions actives
              </Typography>
              <Button variant="outlined" color="error">
                Déconnecter tous les appareils
              </Button>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Sauvegarde des données
              </Typography>
              <Button variant="outlined" color="success">
                Exporter mes données
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )
    };

    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          ⚙️ Paramètres Vétérinaire
        </Typography>
        {settingsContent[currentSection] || settingsContent['settings-general']}
        <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
          <Button variant="contained" startIcon={<SaveIcon />}>
            Sauvegarder
          </Button>
          <Button variant="outlined" startIcon={<CancelIcon />}>
            Annuler
          </Button>
        </Box>
      </Container>
    );
  };

  // Main render function with section switching
  const renderCurrentSection = () => {
    switch (currentSection) {
      case 'profile':
        return renderProfileSection();
      case 'prescriptions':
        return renderPrescriptionsSection();
      case 'consultations':
        return renderConsultationsSection();
      case 'historique':
        return renderHistoriqueSection();
      case 'settings-general':
      case 'settings-security':
        return renderSettingsSection();
      default:
        return renderDashboardSection();
    }
  };

  return renderCurrentSection();
};

export default VeterinaireDashboard;
