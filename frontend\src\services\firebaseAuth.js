// Import des modules Firebase nécessaires
import { auth } from './firebaseConfig';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile as updateFirebaseProfile
} from 'firebase/auth';
import axiosInstance from '../utils/axiosConfig';

// Constantes pour la gestion des timeouts
const AUTH_TIMEOUT = 20000; // 20 secondes pour les opérations d'authentification

// Fonction utilitaire pour gérer les timeouts
const withTimeout = (promise, ms, errorMessage) => {
  let timeoutId;
  const timeoutPromise = new Promise((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(errorMessage || `L'opération a dépassé le délai de ${ms/1000} secondes`));
    }, ms);
  });

  return Promise.race([promise, timeoutPromise])
    .finally(() => clearTimeout(timeoutId));
};

// Service d'authentification Firebase avec synchronisation PostgreSQL
class FirebaseAuthService {
  // Inscription d'un utilisateur
  async register(userData) {
    try {
      // Créer l'utilisateur dans Firebase avec timeout
      const firebaseUser = await withTimeout(
        createUserWithEmailAndPassword(auth, userData.email, userData.password),
        AUTH_TIMEOUT,
        'L\'inscription a pris trop de temps, veuillez réessayer'
      );

      // Mettre à jour le profil Firebase
      await withTimeout(
        updateFirebaseProfile(firebaseUser.user, {
          displayName: `${userData.first_name} ${userData.last_name}`
        }),
        AUTH_TIMEOUT,
        'La mise à jour du profil a pris trop de temps'
      );

      // Synchroniser avec PostgreSQL via notre API
      const response = await withTimeout(        axiosInstance.post('/api/auth/register', {
          ...userData,
          firebase_uid: firebaseUser.user.uid
        }),
        AUTH_TIMEOUT,
        'La synchronisation avec le serveur a pris trop de temps'
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      throw this.handleFirebaseError(error);
    }
  }

  // Connexion d'un utilisateur
  async login(email, password) {
    try {
      // Connexion Firebase avec timeout
      const firebaseUser = await withTimeout(
        signInWithEmailAndPassword(auth, email, password),
        AUTH_TIMEOUT,
        'La connexion a pris trop de temps, veuillez réessayer'
      );

      // Obtenir le token Firebase avec timeout
      const token = await withTimeout(
        firebaseUser.user.getIdToken(),
        AUTH_TIMEOUT,
        'L\'obtention du token a pris trop de temps'
      );

      // Synchroniser avec notre backend
      const response = await withTimeout(        axiosInstance.post('/api/auth/login', {
          email,
          firebase_token: token,
          firebase_uid: firebaseUser.user.uid,
          display_name: firebaseUser.user.displayName
        }),
        AUTH_TIMEOUT,
        'La synchronisation avec le serveur a pris trop de temps'
      );

      // Stocker le token JWT et les credentials dans localStorage
      const jwtToken = response.data.token;
      localStorage.setItem('token', jwtToken);
      localStorage.setItem('user_credentials', JSON.stringify({ email, password }));

      return {
        token: jwtToken,
        user: response.data.user
      };
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      throw this.handleFirebaseError(error);
    }
  }

  // Déconnexion
  async logout() {
    try {
      await withTimeout(
        signOut(auth),
        AUTH_TIMEOUT,
        'La déconnexion a pris trop de temps'
      );
      localStorage.removeItem('token');
      localStorage.removeItem('user_credentials');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
      throw this.handleFirebaseError(error);
    }
  }

  // Réinitialisation du mot de passe
  async resetPassword(email) {
    try {
      await withTimeout(
        sendPasswordResetEmail(auth, email),
        AUTH_TIMEOUT,
        'La réinitialisation du mot de passe a pris trop de temps'
      );
    } catch (error) {
      console.error('Erreur lors de la réinitialisation du mot de passe:', error);
      throw this.handleFirebaseError(error);
    }
  }

  // Gestion des erreurs Firebase
  handleFirebaseError(error) {
    console.error('Erreur Firebase détaillée:', {
      code: error.code,
      message: error.message,
      stack: error.stack
    });

    let message = 'Une erreur est survenue';

    switch (error.code) {
      case 'auth/email-already-in-use':
        message = 'Cette adresse email est déjà utilisée';
        break;
      case 'auth/invalid-email':
        message = 'Adresse email invalide';
        break;
      case 'auth/operation-not-allowed':
        message = 'Opération non autorisée';
        break;
      case 'auth/weak-password':
        message = 'Le mot de passe est trop faible';
        break;
      case 'auth/user-disabled':
        message = 'Ce compte a été désactivé';
        break;
      case 'auth/user-not-found':
        message = 'Aucun compte ne correspond à cette adresse email';
        break;
      case 'auth/wrong-password':
        message = 'Mot de passe incorrect';
        break;
      case 'auth/too-many-requests':
        message = 'Trop de tentatives de connexion. Veuillez réessayer plus tard';
        break;
      case 'auth/network-request-failed':
        message = 'Problème de connexion réseau. Vérifiez votre connexion internet';
        break;
      case 'auth/invalid-credential':
        message = 'Les informations de connexion sont invalides. Veuillez vérifier vos identifiants.';
        break;
      case 'auth/requires-recent-login':
        message = 'Cette opération nécessite une connexion récente. Veuillez vous reconnecter.';
        break;
      default:
        if (error.message && error.message.includes('timeout')) {
          message = error.message;
        } else {
          console.warn('Code d\'erreur Firebase non géré:', error.code);
        }
        break;
    }

    return new Error(message);
  }
}

export const firebaseAuth = new FirebaseAuthService();
