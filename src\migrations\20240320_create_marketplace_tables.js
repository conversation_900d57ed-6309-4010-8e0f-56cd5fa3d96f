const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'poultraydz',
  password: process.env.DB_PASSWORD || 'root',
  port: process.env.DB_PORT || 5432,
});

async function up() {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Table des produits
    await client.query(`
      CREATE TABLE IF NOT EXISTS produits (
        id SERIAL PRIMARY KEY,
        nom VARCHAR(255) NOT NULL,
        description TEXT,
        prix DECIMAL(10,2) NOT NULL,
        stock_disponible INTEGER NOT NULL DEFAULT 0,
        image_url TEXT,
        categorie VARCHAR(50),
        status VARCHAR(20) DEFAULT 'actif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table du panier
    await client.query(`
      CREATE TABLE IF NOT EXISTS panier (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        produit_id INTEGER NOT NULL REFERENCES produits(id),
        quantity INTEGER NOT NULL DEFAULT 1,
        status VARCHAR(20) DEFAULT 'actif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des favoris
    await client.query(`
      CREATE TABLE IF NOT EXISTS favoris (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        produit_id INTEGER NOT NULL REFERENCES produits(id),
        status VARCHAR(20) DEFAULT 'actif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des commandes
    await client.query(`
      CREATE TABLE IF NOT EXISTS commandes (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        total DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) DEFAULT 'en_attente',
        adresse_livraison TEXT,
        telephone_livraison VARCHAR(20),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des détails de commande
    await client.query(`
      CREATE TABLE IF NOT EXISTS commande_details (
        id SERIAL PRIMARY KEY,
        commande_id INTEGER NOT NULL REFERENCES commandes(id),
        produit_id INTEGER NOT NULL REFERENCES produits(id),
        quantity INTEGER NOT NULL,
        prix_unitaire DECIMAL(10,2) NOT NULL,
        sous_total DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Trigger pour mettre à jour updated_at
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    // Appliquer le trigger aux tables
    const tables = ['produits', 'panier', 'favoris', 'commandes'];
    for (const table of tables) {
      await client.query(`
        DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};
        CREATE TRIGGER update_${table}_updated_at
        BEFORE UPDATE ON ${table}
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `);
    }

    // Indices pour améliorer les performances
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_panier_user ON panier(user_id);
      CREATE INDEX IF NOT EXISTS idx_panier_status ON panier(status);
      CREATE INDEX IF NOT EXISTS idx_favoris_user ON favoris(user_id);
      CREATE INDEX IF NOT EXISTS idx_favoris_status ON favoris(status);
      CREATE INDEX IF NOT EXISTS idx_commandes_user ON commandes(user_id);
      CREATE INDEX IF NOT EXISTS idx_commandes_status ON commandes(status);
      CREATE INDEX IF NOT EXISTS idx_produits_status ON produits(status);
      CREATE INDEX IF NOT EXISTS idx_produits_categorie ON produits(categorie);
    `);

    await client.query('COMMIT');
    console.log('Migration marketplace réussie');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la migration marketplace:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function down() {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Supprimer les indices
    await client.query(`
      DROP INDEX IF EXISTS idx_panier_user;
      DROP INDEX IF EXISTS idx_panier_status;
      DROP INDEX IF EXISTS idx_favoris_user;
      DROP INDEX IF EXISTS idx_favoris_status;
      DROP INDEX IF EXISTS idx_commandes_user;
      DROP INDEX IF EXISTS idx_commandes_status;
      DROP INDEX IF EXISTS idx_produits_status;
      DROP INDEX IF EXISTS idx_produits_categorie;
    `);

    // Supprimer le trigger et la fonction
    const tables = ['produits', 'panier', 'favoris', 'commandes'];
    for (const table of tables) {
      await client.query(`DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};`);
    }
    await client.query('DROP FUNCTION IF EXISTS update_updated_at_column;');

    // Supprimer les tables dans l'ordre inverse des dépendances
    await client.query(`
      DROP TABLE IF EXISTS commande_details;
      DROP TABLE IF EXISTS commandes;
      DROP TABLE IF EXISTS favoris;
      DROP TABLE IF EXISTS panier;
      DROP TABLE IF EXISTS produits;
    `);

    await client.query('COMMIT');
    console.log('Rollback marketplace réussi');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors du rollback marketplace:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = { up, down };