console.log('Starting minimal server...');

const express = require('express');
const app = express();
const PORT = 3003;

app.use(express.json());

app.get('/', (req, res) => {
  console.log('Root endpoint called');
  res.json({ message: 'Minimal server is working' });
});

app.get('/api/test', (req, res) => {
  console.log('Test API endpoint called');
  res.json({ message: 'Test API is working', timestamp: new Date() });
});

app.get('/api/veterinaire/notifications', (req, res) => {
  console.log('Veterinaire notifications endpoint called');
  res.json({
    status: 'success',
    data: {
      notifications: [
        {
          type: 'info',
          id: 1,
          date: new Date(),
          message: 'Test notification',
          details: 'This is a test notification',
          priorite: 'info'
        }
      ]
    }
  });
});

app.listen(PORT, () => {
  console.log(`Minimal server running on http://localhost:${PORT}`);
});

console.log('Server setup complete');
