/**
 * Direct test of veterinaire endpoints without authentication
 */
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Sequelize, QueryTypes } = require('sequelize');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Database connection
const sequelize = new Sequelize(
  process.env.DB_NAME || 'poultraydz',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || 'root',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: false
  }
);

// Mock auth middleware for testing
const mockAuth = (req, res, next) => {
  req.user = {
    id: 1,
    role: 'veterinaire'
  };
  next();
};

// Test veterinaire dashboard endpoint
app.get('/api/veterinaire/dashboard', mockAuth, async (req, res) => {
  try {
    console.log('🩺 Dashboard request received for user:', req.user.id);
    
    const veterinaireId = req.user.id;

    // Check if required tables exist first
    let tablesExist = {};
    try {
      const tableCheck = await sequelize.query(
        `SELECT 
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'prescriptions') as prescriptions_exists,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'consultations') as consultations_exists,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'eleveurs') as eleveurs_exists
        `,
        { type: QueryTypes.SELECT }
      );
      tablesExist = tableCheck[0];
      console.log('📊 Tables existence check:', tablesExist);
    } catch (tableError) {
      console.error('❌ Error checking tables:', tableError.message);
      tablesExist = { prescriptions_exists: false, consultations_exists: false, eleveurs_exists: false };
    }

    // Return mock data for now
    const mockData = {
      status: 'success',
      data: {
        stats: {
          totalPrescriptions: 0,
          prescriptionsMois: 0,
          totalConsultations: 0,
          consultationsMois: 0,
          eleveursSuivis: 0,
          consultationsSemaineProchaine: 0,
          prescriptionsActives: 0,
          satisfactionMoyenne: 0,
          coutMoyenConsultation: 0
        },
        consultationsAVenir: [],
        prescriptionsRecentes: [],
        consultationsHistorique: [],
        graphiques: {
          consultationsParMois: [],
          typesConsultations: []
        },
        alertesSante: []
      }
    };

    console.log('✅ Dashboard data prepared successfully');
    res.json(mockData);

  } catch (error) {
    console.error('❌ Dashboard error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors du chargement du dashboard',
      error: error.message
    });
  }
});

// Test veterinaire notifications endpoint
app.get('/api/veterinaire/notifications', mockAuth, async (req, res) => {
  try {
    console.log('🔔 Notifications request received for user:', req.user.id);
    
    // Return mock notifications
    const mockNotifications = {
      status: 'success',
      data: {
        notifications: [
          {
            type: 'info',
            id: 1,
            date: new Date(),
            message: 'Bienvenue dans votre espace vétérinaire',
            details: 'Votre tableau de bord est prêt à être utilisé',
            priorite: 'info',
            created_at: new Date()
          },
          {
            type: 'consultation',
            id: 2,
            date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
            message: 'Consultation programmée avec Jean Dupont',
            details: 'Contrôle de routine pour les poules pondeuses',
            priorite: 'info',
            created_at: new Date()
          }
        ]
      }
    };

    console.log('✅ Notifications data prepared successfully');
    res.json(mockNotifications);

  } catch (error) {
    console.error('❌ Notifications error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la récupération des notifications',
      error: error.message
    });
  }
});

// Test route
app.get('/api/test', (req, res) => {
  console.log('📡 Test API called');
  res.json({ message: 'API is working', timestamp: new Date() });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test server running on http://localhost:${PORT}`);
  console.log(`📡 Test API: http://localhost:${PORT}/api/test`);
  console.log(`🩺 Dashboard: http://localhost:${PORT}/api/veterinaire/dashboard`);
  console.log(`🔔 Notifications: http://localhost:${PORT}/api/veterinaire/notifications`);
});

// Test database connection
sequelize.authenticate()
  .then(() => {
    console.log('✅ Database connection established successfully.');
  })
  .catch(err => {
    console.error('❌ Unable to connect to the database:', err.message);
  });
