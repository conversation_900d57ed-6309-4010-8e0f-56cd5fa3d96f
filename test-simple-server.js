console.log('🔄 Starting server...');
require('dotenv').config();
console.log('✅ Environment loaded');
const express = require('express');
console.log('✅ Express loaded');
const cors = require('cors');
console.log('✅ CORS loaded');

const app = express();
const PORT = process.env.PORT || 3003;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.json({ message: 'Server is running', port: PORT });
});

// Test API route
app.get('/api/test', (req, res) => {
  console.log('📡 Test API called');
  res.json({ message: 'API is working', timestamp: new Date() });
});

// Test veterinaire route without auth
app.get('/api/veterinaire/test', (req, res) => {
  console.log('🩺 Veterinaire test API called');
  res.json({
    message: 'Veterinaire API is working',
    timestamp: new Date(),
    user: { id: 1, role: 'veterinaire' }
  });
});

// Import and use veterinaire routes
try {
  const veterinaireRoutes = require('./src/routes/veterinaireRoutes');
  app.use('/api/veterinaire', veterinaireRoutes);
  console.log('✅ Veterinaire routes loaded');
} catch (error) {
  console.error('❌ Error loading veterinaire routes:', error.message);
}

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on http://localhost:${PORT}`);
  console.log(`📡 Test API: http://localhost:${PORT}/api/test`);
  console.log(`🩺 Veterinaire API: http://localhost:${PORT}/api/veterinaire/notifications`);
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection:', reason);
});
