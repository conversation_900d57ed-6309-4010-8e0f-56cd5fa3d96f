/**
 * Configuration Axios pour Poultray DZ
 * Ce fichier configure une instance Axios avec des intercepteurs pour gérer l'authentification
 * et les erreurs de manière cohérente dans toute l'application.
 */

import axios from 'axios';

// URL de base de l'API
const API_URL = 'http://localhost:3003';

// Configuration des retries
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 seconde entre chaque retry
const RETRY_STATUS_CODES = [408, 429, 500, 502, 503, 504]; // Codes d'erreur à réessayer

// Création d'une instance Axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000, // 30 secondes de timeout
  headers: {
    'Content-Type': 'application/json'
  }
});

// Fonction utilitaire pour attendre un délai
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Intercepteur pour les requêtes - ajoute le token à chaque requête
axiosInstance.interceptors.request.use(
  config => {
    // Ajouter le compteur de retries à la configuration
    config.retryCount = config.retryCount || 0;
    const token = localStorage.getItem('token');

    if (token) {
      // Utiliser à la fois l'en-tête standard Authorization et x-auth-token
      config.headers['Authorization'] = `Bearer ${token}`;
      config.headers['x-auth-token'] = token;
    }

    // Supprimer le double /api dans l'URL si présent
    if (config.url.startsWith('/api/api/')) {
      config.url = config.url.replace('/api/api/', '/api/');
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Intercepteur pour les réponses - gère les erreurs et les retries
axiosInstance.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;

    // Vérifier si l'erreur doit être réessayée
    if (!config || 
        config.retryCount >= MAX_RETRIES || 
        !error.response || 
        !RETRY_STATUS_CODES.includes(error.response.status)) {
      // Log détaillé de l'erreur
      console.error('Erreur API:', {
        url: config?.url,
        method: config?.method,
        status: error.response?.status,
        data: error.response?.data,
        retryCount: config?.retryCount
      });
      return Promise.reject(error);
    }

    // Incrémenter le compteur de retries
    config.retryCount += 1;

    // Attendre avant de réessayer
    await wait(RETRY_DELAY * config.retryCount);

    // Réessayer la requête
    return axiosInstance(config);
  }
);

export default axiosInstance;
