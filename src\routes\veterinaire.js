const express = require('express');
const router = express.Router();
const { authenticateToken, checkRole } = require('../middleware/auth');
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'poultraydz',
  password: process.env.DB_PASSWORD || 'root',
  port: process.env.DB_PORT || 5432,
});

// Middleware pour vérifier le rôle vétérinaire
const isVeterinaire = checkRole('veterinaire');

// Récupérer les données du dashboard
router.get('/dashboard', [authenticateToken, isVeterinaire], async (req, res) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Statistiques générales
    const statsQuery = await client.query(`
      SELECT 
        COUNT(DISTINCT p.id) as total_prescriptions,
        COUNT(DISTINCT c.id) as total_consultations,
        COUNT(DISTINCT c.eleveur_id) as eleveurs_suivis,
        COUNT(DISTINCT CASE 
          WHEN c.date_consultation >= NOW() AND c.date_consultation <= NOW() + INTERVAL '7 days'
          THEN c.id 
        END) as consultations_semaine_prochaine,
        COUNT(DISTINCT CASE 
          WHEN p.created_at >= DATE_TRUNC('month', NOW())
          THEN p.id 
        END) as prescriptions_mois,
        COUNT(DISTINCT CASE 
          WHEN c.date_consultation >= DATE_TRUNC('month', NOW())
          THEN c.id 
        END) as consultations_mois
      FROM veterinaires v
      LEFT JOIN prescriptions p ON v.id = p.veterinaire_id
      LEFT JOIN consultations c ON v.id = c.veterinaire_id
      WHERE v.user_id = $1
    `, [req.user.id]);

    // Consultations à venir
    const consultationsQuery = await client.query(`
      SELECT c.id, c.date_consultation, c.motif, c.urgence,
             u.nom as eleveur_nom, u.prenom as eleveur_prenom
      FROM consultations c
      JOIN veterinaires v ON c.veterinaire_id = v.id
      JOIN users u ON c.eleveur_id = u.id
      WHERE v.user_id = $1 AND c.date_consultation >= NOW()
      ORDER BY c.date_consultation ASC
      LIMIT 10
    `, [req.user.id]);

    // Prescriptions récentes
    const prescriptionsQuery = await client.query(`
      SELECT p.id, p.medicament, p.dosage, p.created_at, p.status,
             u.nom as eleveur_nom, u.prenom as eleveur_prenom
      FROM prescriptions p
      JOIN veterinaires v ON p.veterinaire_id = v.id
      JOIN users u ON p.eleveur_id = u.id
      WHERE v.user_id = $1
      ORDER BY p.created_at DESC
      LIMIT 10
    `, [req.user.id]);

    // Historique des consultations
    const historiqueQuery = await client.query(`
      SELECT c.id, c.date_consultation, c.motif, c.status,
             u.nom as eleveur_nom, u.prenom as eleveur_prenom
      FROM consultations c
      JOIN veterinaires v ON c.veterinaire_id = v.id
      JOIN users u ON c.eleveur_id = u.id
      WHERE v.user_id = $1 AND c.date_consultation < NOW()
      ORDER BY c.date_consultation DESC
      LIMIT 10
    `, [req.user.id]);

    // Graphiques - Consultations par mois
    const graphiquesQuery = await client.query(`
      SELECT 
        DATE_TRUNC('month', c.date_consultation) as mois,
        COUNT(*) as nombre_consultations,
        COUNT(CASE WHEN c.status = 'terminee' THEN 1 END) as consultations_terminees
      FROM consultations c
      JOIN veterinaires v ON c.veterinaire_id = v.id
      WHERE v.user_id = $1
        AND c.date_consultation >= NOW() - INTERVAL '6 months'
      GROUP BY DATE_TRUNC('month', c.date_consultation)
      ORDER BY mois ASC
    `, [req.user.id]);

    // Alertes santé
    const alertesQuery = await client.query(`
      SELECT a.id, a.titre, a.message, a.priorite, a.date_declenchement,
             u.nom as eleveur_nom, u.prenom as eleveur_prenom
      FROM alertes_sante a
      JOIN veterinaires v ON a.veterinaire_id = v.id
      JOIN users u ON a.eleveur_id = u.id
      WHERE v.user_id = $1
      ORDER BY a.date_declenchement DESC
      LIMIT 5
    `, [req.user.id]);

    await client.query('COMMIT');

    res.json({
      success: true,
      data: {
        stats: statsQuery.rows[0],
        consultationsAVenir: consultationsQuery.rows,
        prescriptionsRecentes: prescriptionsQuery.rows,
        consultationsHistorique: historiqueQuery.rows,
        graphiques: {
          consultationsParMois: graphiquesQuery.rows
        },
        alertesSante: alertesQuery.rows
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la récupération des données du dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des données du dashboard',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Récupérer les notifications
router.get('/notifications', [authenticateToken, isVeterinaire], async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT n.id, n.type, n.message, n.lu, n.created_at,
             u.nom as eleveur_nom, u.prenom as eleveur_prenom
      FROM notifications n
      JOIN veterinaires v ON n.veterinaire_id = v.id
      LEFT JOIN users u ON n.eleveur_id = u.id
      WHERE v.user_id = $1
      ORDER BY n.created_at DESC
      LIMIT 20
    `, [req.user.id]);

    res.json({
      success: true,
      data: {
        notifications: result.rows
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notifications',
      error: error.message
    });
  }
});

// Créer une consultation rapide
router.post('/consultations/quick', [authenticateToken, isVeterinaire], async (req, res) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const { eleveur_id, date_consultation, motif, urgence } = req.body;

    // Récupérer l'ID du vétérinaire
    const vetQuery = await client.query(
      'SELECT id FROM veterinaires WHERE user_id = $1',
      [req.user.id]
    );

    if (vetQuery.rows.length === 0) {
      throw new Error('Vétérinaire non trouvé');
    }

    // Créer la consultation
    const result = await client.query(`
      INSERT INTO consultations (
        veterinaire_id, eleveur_id, date_consultation, motif, urgence, status
      ) VALUES ($1, $2, $3, $4, $5, 'programmee')
      RETURNING id
    `, [vetQuery.rows[0].id, eleveur_id, date_consultation, motif, urgence]);

    // Créer une notification pour l'éleveur
    await client.query(`
      INSERT INTO notifications (type, message, eleveur_id, veterinaire_id)
      VALUES ('nouvelle_consultation', $1, $2, $3)
    `, [
      `Nouvelle consultation programmée pour le ${new Date(date_consultation).toLocaleDateString()}`,
      eleveur_id,
      vetQuery.rows[0].id
    ]);

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'Consultation créée avec succès',
      data: { id: result.rows[0].id }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la création de la consultation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la consultation',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Créer une prescription rapide
router.post('/prescriptions/quick', [authenticateToken, isVeterinaire], async (req, res) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const { eleveur_id, volaille_id, medicament, dosage, duree_traitement, instructions } = req.body;

    // Récupérer l'ID du vétérinaire
    const vetQuery = await client.query(
      'SELECT id FROM veterinaires WHERE user_id = $1',
      [req.user.id]
    );

    if (vetQuery.rows.length === 0) {
      throw new Error('Vétérinaire non trouvé');
    }

    // Créer la prescription
    const result = await client.query(`
      INSERT INTO prescriptions (
        veterinaire_id, eleveur_id, volaille_id, medicament, dosage,
        duree_traitement, instructions, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'active')
      RETURNING id
    `, [
      vetQuery.rows[0].id, eleveur_id, volaille_id, medicament,
      dosage, duree_traitement, instructions
    ]);

    // Créer une notification pour l'éleveur
    await client.query(`
      INSERT INTO notifications (type, message, eleveur_id, veterinaire_id)
      VALUES ('nouvelle_prescription', $1, $2, $3)
    `, [
      `Nouvelle prescription créée pour ${medicament}`,
      eleveur_id,
      vetQuery.rows[0].id
    ]);

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'Prescription créée avec succès',
      data: { id: result.rows[0].id }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la création de la prescription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la prescription',
      error: error.message
    });
  } finally {
    client.release();
  }
});

module.exports = router;