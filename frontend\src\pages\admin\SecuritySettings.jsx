import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Grid,
  FormControlLabel,
  Switch,
  Divider,
  Slider,
  InputAdornment,
  MenuItem,
  Snackbar,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Save as SaveIcon,
  Security as SecurityIcon,
  LockClock as LockClockIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
  VpnKey as VpnKeyIcon,
  VerifiedUser as VerifiedUserIcon
} from '@mui/icons-material';
import settingsService from '../../services/settingsService';
import { useLanguage } from '../../contexts/LanguageContext';

const SecuritySettings = () => {
  const { t } = useLanguage();

  const [settings, setSettings] = useState({
    enable2FA: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    passwordComplexityRegex: '',
    passwordHistoryCount: 3,
    passwordExpiryDays: 90,
    contentSecurityPolicy: '',
    corsAllowedOrigins: '',
    logLevel: 'info',
    apiRateLimitingEnabled: true,
    apiRateLimitRequests: 100,
    apiRateLimitWindowMs: 900000 // 15 minutes in milliseconds
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const data = await settingsService.fetchSecuritySettings();
        if (data) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ...data
          }));
        }
        setError('');
      } catch (err) {
        setError(err.response?.data?.message || t('errors.fetchingSecuritySettings', 'Error fetching security settings'));
        console.error("Error fetchSecuritySettings:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchSettings();
  }, [t]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' || type === 'switch' ? checked : value
    }));
  };

  const handleSliderChange = (name) => (e, value) => {
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const response = await settingsService.updateSecuritySettings(settings);
      setSuccess(t('success.securitySettingsSaved', 'Security settings saved successfully!'));
      setToast({
        open: true,
        message: t('success.securitySettingsSaved', 'Security settings saved successfully!'),
        severity: 'success'
      });
    } catch (err) {
      setError(err.response?.data?.message || t('errors.savingSecuritySettings', 'Error saving security settings'));
      setToast({
        open: true,
        message: err.response?.data?.message || t('errors.savingSecuritySettings', 'Error saving security settings'),
        severity: 'error'
      });
      console.error("Error handleSubmit:", err);
    } finally {
      setSaving(false);
    }
  };

  const handleCloseToast = () => {
    setToast({ ...toast, open: false });
  };

  const logLevels = [
    { value: 'debug', label: 'Debug' },
    { value: 'info', label: 'Info' },
    { value: 'warn', label: 'Warning' },
    { value: 'error', label: 'Error' }
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 4, m: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        {t('settings.security.title', 'Security Settings')}
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      <form onSubmit={handleSubmit}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          {t('settings.security.authentication', 'Authentication')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.enable2FA}
                  onChange={handleChange}
                  name="enable2FA"
                  color="primary"
                />
              }
              label={t('settings.security.enable2FA', 'Enable Two-Factor Authentication')}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.security.sessionTimeout', 'Session Timeout (minutes)')}
              name="sessionTimeout"
              value={settings.sessionTimeout}
              onChange={handleChange}
              variant="outlined"
              type="number"
              InputProps={{
                inputProps: { min: 5, max: 1440 },
                endAdornment: <InputAdornment position="end">{t('units.minutes', 'min')}</InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.security.maxLoginAttempts', 'Max Login Attempts')}
              name="maxLoginAttempts"
              value={settings.maxLoginAttempts}
              onChange={handleChange}
              variant="outlined"
              type="number"
              InputProps={{
                inputProps: { min: 1, max: 10 }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.security.lockoutDuration', 'Account Lockout Duration (minutes)')}
              name="lockoutDuration"
              value={settings.lockoutDuration}
              onChange={handleChange}
              variant="outlined"
              type="number"
              InputProps={{
                inputProps: { min: 5, max: 1440 },
                endAdornment: <InputAdornment position="end">{t('units.minutes', 'min')}</InputAdornment>,
              }}
            />
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
          {t('settings.security.passwordPolicy', 'Password Policy')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('settings.security.passwordComplexityRegex', 'Password Complexity Regex')}
              name="passwordComplexityRegex"
              value={settings.passwordComplexityRegex}
              onChange={handleChange}
              variant="outlined"
              placeholder="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
              helperText={t('settings.security.passwordRegexHelp', 'Example: Minimum 8 characters, uppercase, lowercase, number and special character')}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.security.passwordHistoryCount', 'Password History Count')}
              name="passwordHistoryCount"
              value={settings.passwordHistoryCount}
              onChange={handleChange}
              variant="outlined"
              type="number"
              InputProps={{
                inputProps: { min: 0, max: 24 }
              }}
              helperText={t('settings.security.passwordHistoryHelp', 'Number of previous passwords users cannot reuse')}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('settings.security.passwordExpiryDays', 'Password Expiry (days)')}
              name="passwordExpiryDays"
              value={settings.passwordExpiryDays}
              onChange={handleChange}
              variant="outlined"
              type="number"
              InputProps={{
                inputProps: { min: 0, max: 365 },
                endAdornment: <InputAdornment position="end">{t('units.days', 'days')}</InputAdornment>,
              }}
              helperText={t('settings.security.passwordExpiryHelp', 'Set to 0 to disable password expiry')}
            />
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
          {t('settings.security.apiSecurity', 'API Security')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.apiRateLimitingEnabled}
                  onChange={handleChange}
                  name="apiRateLimitingEnabled"
                  color="primary"
                />
              }
              label={t('settings.security.enableRateLimiting', 'Enable API Rate Limiting')}
            />
          </Grid>

          {settings.apiRateLimitingEnabled && (
            <>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('settings.security.apiRateLimitRequests', 'API Rate Limit Requests')}
                  name="apiRateLimitRequests"
                  value={settings.apiRateLimitRequests}
                  onChange={handleChange}
                  variant="outlined"
                  type="number"
                  InputProps={{
                    inputProps: { min: 10, max: 1000 }
                  }}
                  helperText={t('settings.security.apiRateLimitRequestsHelp', 'Maximum requests allowed per time window')}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('settings.security.apiRateLimitWindowMin', 'Rate Limit Window (minutes)')}
                  name="apiRateLimitWindowMs"
                  // Convert from milliseconds to minutes for display
                  value={settings.apiRateLimitWindowMs / 60000}
                  onChange={(e) => {
                    // Convert minutes to milliseconds for storage
                    const minutes = Number(e.target.value);
                    handleChange({
                      target: {
                        name: 'apiRateLimitWindowMs',
                        value: minutes * 60000
                      }
                    });
                  }}
                  variant="outlined"
                  type="number"
                  InputProps={{
                    inputProps: { min: 1, max: 60 },
                    endAdornment: <InputAdornment position="end">{t('units.minutes', 'min')}</InputAdornment>,
                  }}
                />
              </Grid>
            </>
          )}

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('settings.security.corsAllowedOrigins', 'CORS Allowed Origins')}
              name="corsAllowedOrigins"
              value={settings.corsAllowedOrigins}
              onChange={handleChange}
              variant="outlined"
              placeholder="https://example.com, https://app.example.com"
              helperText={t('settings.security.corsHelp', 'Comma-separated list of allowed origins (leave empty for all origins)')}
            />
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
          {t('settings.security.logging', 'Logging')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="log-level-label">{t('settings.security.logLevel', 'Log Level')}</InputLabel>
              <Select
                labelId="log-level-label"
                name="logLevel"
                value={settings.logLevel}
                onChange={handleChange}
                label={t('settings.security.logLevel', 'Log Level')}
              >
                {logLevels.map((level) => (
                  <MenuItem key={level.value} value={level.value}>
                    {level.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('settings.security.contentSecurityPolicy', 'Content Security Policy')}
              name="contentSecurityPolicy"
              value={settings.contentSecurityPolicy}
              onChange={handleChange}
              variant="outlined"
              placeholder="default-src 'self'; script-src 'self' https://trusted-cdn.com"
              helperText={t('settings.security.cspHelp', 'CSP header value to enhance security (leave empty for default)')}
              multiline
              rows={2}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={saving}
          >
            {saving ? <CircularProgress size={24} /> : t('actions.saveSettings', 'Save Settings')}
          </Button>
        </Box>
      </form>

      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleCloseToast}
        message={toast.message}
        severity={toast.severity}
      />
    </Paper>
  );
};

export default SecuritySettings;
