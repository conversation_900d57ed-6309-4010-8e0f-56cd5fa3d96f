import React, { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Typography,
  Collapse,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  Pets as PetsIcon,
  BarChart as ChartIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ExpandLess,
  ExpandMore,
  Article as ArticleIcon,
  Translate as TranslateIcon,
  Notifications as NotificationsIcon,
  MedicalServices as MedicalIcon,
  Store as StoreIcon,
  Psychology as AIIcon,
  Language as LanguageIcon,
  Email as EmailIcon,
  Event as EventIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

// Styles pour la barre latérale
const sidebarStyles = {
  menuItem: {
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
      borderRadius: '8px',
      margin: '0 8px',
    },
    '&.Mui-selected': {
      backgroundColor: 'primary.light',
      borderRadius: '8px',
      margin: '0 8px',
      '&:hover': {
        backgroundColor: 'primary.light',
      },
    },
  },
  icon: {
    color: 'primary.main',
  },
  text: {
    fontWeight: 500,
  },
  divider: {
    margin: '16px 0',
  },
  iconContainer: {
    borderRadius: '8px',
    padding: '8px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      transform: 'scale(1.1)',
    },
  },
  listItemButton: {
    borderRadius: '8px',
    margin: '4px 8px',
    transition: 'all 0.2s ease',
    '&.Mui-selected': {
      backgroundColor: 'rgba(0, 0, 0, 0.08)',
      '&:before': {
        content: '""',
        position: 'absolute',
        left: 0,
        top: '20%',
        height: '60%',
        width: '4px',
        backgroundColor: 'primary.main',
        borderRadius: '0 4px 4px 0',
      },
    },
  },
};

// Couleurs thématiques pour les icônes
const iconColors = {
  dashboard: '#4caf50', // vert
  profile: '#2196f3', // bleu
  homepage: '#ff9800', // orange
  users: '#9c27b0', // violet
  volailles: '#e91e63', // rose
  statistics: '#00bcd4', // cyan
  blog: '#607d8b', // bleu-gris
  ai: '#673ab7', // violet foncé
  translations: '#3f51b5', // indigo
  notifications: '#f44336', // rouge
  prescriptions: '#8bc34a', // vert clair
  consultations: '#cddc39', // lime
  historique: '#795548', // marron
  produits: '#ff5722', // orange foncé
  commandes: '#ffc107', // ambre
  ventes: '#009688', // teal
  settings: '#757575', // gris
};


// Drawer width
const drawerWidth = 260;

// Menu items configuration based on user roles
const getMenuItems = (role, handleLoginAsUser) => {
  // Common menu items for all users
  const commonItems = [
    {
      title: 'Tableau de bord',
      icon: <DashboardIcon />,
      path: `/${role}/dashboard`,
      color: iconColors.dashboard,
    },
    {
      title: 'Profil',
      icon: <PersonIcon />,
      path: `/${role}/profile`,
      color: iconColors.profile,
    },
  ];

  // Role-specific menu items
  const roleSpecificItems = {
    admin: [
      {
        title: 'Page d\'accueil',
        icon: <ArticleIcon />,
        path: '/admin/homepage',
        color: iconColors.homepage,
        sx: sidebarStyles.menuItem,
      },
      {
        title: 'Gestion des utilisateurs',
        icon: <PersonIcon />,
        color: iconColors.users,
        items: [
          { title: 'Tous les utilisateurs', path: '/admin/users' },
          { title: 'Éleveurs', path: '/admin/users/eleveurs' },
          { title: 'Vétérinaires', path: '/admin/users/veterinaires' },
          { title: 'Marchands', path: '/admin/users/marchands' },
          { title: 'Connexion en tant que...', path: '/admin/users/login-as' }, // Updated path
        ],
        sx: sidebarStyles.menuItem,
      },
      {
        title: 'Volailles',
        icon: <PetsIcon />,
        path: '/admin/volailles',
        color: iconColors.volailles,
      },
      {
        title: 'Statistiques',
        icon: <ChartIcon />,
        path: '/admin/statistics',
        color: iconColors.statistics,
      },
      {
        title: 'Blog',
        icon: <ArticleIcon />,
        path: '/admin/blog',
        color: iconColors.blog,
      },
      {
        title: 'Intelligence Artificielle',
        icon: <AIIcon />,
        color: iconColors.ai,
        items: [
          { title: 'Générateur de blog', path: '/admin/ai/blog-generator' },
          { title: 'Analyse de données', path: '/admin/ai/data-analysis' },
          { title: 'Contenu de page', path: '/admin/ai/page-content' },
          { title: 'Configuration API', path: '/admin/ai/api-config' }, // Correct path
        ],
      },
      {
        title: 'Traductions',
        icon: <TranslateIcon />,
        path: '/admin/translations',
        color: iconColors.translations,
      },
      {
        title: 'Rôles et Plans',
        icon: <SettingsIcon />,
        path: '/admin/roles-plans',
        color: '#ff5722', // orange foncé
      },
      {
        title: 'Notifications',
        icon: <NotificationsIcon />,
        path: '/admin/notifications',
        color: iconColors.notifications,
      },
    ],
    eleveur: [
      {
        title: 'Mes volailles',
        icon: <PetsIcon />,
        path: '/eleveur/volailles',
        color: iconColors.volailles,
      },
      {
        title: 'Mes ventes',
        icon: <StoreIcon />,
        path: '/eleveur/ventes',
        color: iconColors.ventes,
      },
      {
        title: 'Statistiques',
        icon: <ChartIcon />,
        path: '/eleveur/statistics',
        color: iconColors.statistics,
      },
    ],
    veterinaire: [
      {
        key: 'vet-dashboard',
        title: 'Tableau de bord',
        icon: <DashboardIcon />,
        path: '/dashboard/veterinaire',
        color: iconColors.dashboard,
        section: 'dashboard'
      },
      {
        key: 'vet-profile',
        title: 'Profil',
        icon: <PersonIcon />,
        path: '/dashboard/veterinaire',
        color: iconColors.profile,
        section: 'profile'
      },
      {
        key: 'vet-prescriptions',
        title: 'Prescriptions',
        icon: <MedicalIcon />,
        path: '/dashboard/veterinaire',
        color: iconColors.prescriptions,
        section: 'prescriptions'
      },
      {
        key: 'vet-consultations',
        title: 'Consultations',
        icon: <EventIcon />,
        path: '/dashboard/veterinaire',
        color: iconColors.consultations,
        section: 'consultations'
      },
      {
        key: 'vet-historique',
        title: 'Historique',
        icon: <ArticleIcon />,
        path: '/dashboard/veterinaire',
        color: iconColors.historique,
        section: 'historique'
      },
      {
        key: 'vet-settings',
        title: 'Paramètres',
        icon: <SettingsIcon />,
        color: iconColors.settings,
        items: [
          { key: 'vet-settings-general', title: 'Général', path: '/dashboard/veterinaire', section: 'settings-general' },
          { key: 'vet-settings-smtp', title: 'Configuration SMTP', path: '/dashboard/veterinaire', section: 'settings-smtp' },
          { key: 'vet-settings-security', title: 'Sécurité', path: '/dashboard/veterinaire', section: 'settings-security' }
        ]
      },
    ],
    marchand: [
      {
        title: 'Produits',
        icon: <StoreIcon />,
        path: '/marchand/produits',
        color: iconColors.produits,
      },
      {
        title: 'Commandes',
        icon: <StoreIcon />,
        path: '/marchand/commandes',
        color: iconColors.commandes,
      },
      {
        title: 'Ventes',
        icon: <ChartIcon />,
        path: '/marchand/ventes',
        color: iconColors.ventes,
      },
    ],
  };

  // Settings menu item for all users
  const settingsItem = {
    title: 'Paramètres',
    icon: <SettingsIcon />,
    color: iconColors.settings,
    items: [
      { title: 'Général', path: '/admin/settings/general' }, // Correct path
      { title: 'Configuration SMTP', path: '/admin/settings/smtp' }, // Correct path
      { title: 'Sécurité', path: '/admin/settings/security' }, // Correct path
    ],
  };

  return [...commonItems, ...roleSpecificItems[role], settingsItem];
};

const Sidebar = ({ open, toggleDrawer, handleLoginAsUser }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({});

  // If no user or role, show minimal sidebar
  // Handle both string roles (legacy) and object roles (new API format)
  const userRole = user?.role ?
    (typeof user.role === 'object' && user.role !== null ? user.role.name : user.role)
    : 'guest';
  const role = userRole;
  const menuItems = getMenuItems(role, handleLoginAsUser);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleItemClick = (path, section) => {
    if (section) {
      // For section-based navigation, navigate to the base path with section parameter
      navigate(`${path}?section=${section}`);
    } else {
      navigate(path);
    }
    if (window.innerWidth < 600) {
      toggleDrawer(false);
    }
  };

  const toggleExpand = (title) => {
    setExpandedItems((prev) => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  const renderMenuItems = (items) => {
    return items.map((item) => {
      // If item has sub-items, render a collapsible menu
      if (item.items) {
        return (
          <React.Fragment key={item.title}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => toggleExpand(item.title)}
                sx={sidebarStyles.listItemButton}
              >
                <ListItemIcon>
                  <Box sx={{
                    ...sidebarStyles.iconContainer,
                    bgcolor: item.color ? `${item.color}15` : 'transparent',
                  }}>
                    {React.cloneElement(item.icon, { sx: { color: item.color } })}
                  </Box>
                </ListItemIcon>
                <ListItemText primary={item.title} />
                {expandedItems[item.title] ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
            </ListItem>
            <Collapse in={expandedItems[item.title]} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {item.items.map((subItem) => (
                  <ListItemButton
                    key={subItem.key || subItem.title}
                    sx={{ pl: 4, ...sidebarStyles.listItemButton }}
                    onClick={() => subItem.action ? subItem.action() : handleItemClick(subItem.path, subItem.section)}
                    selected={subItem.path ? isActive(subItem.path) : false}
                  >
                    <ListItemText primary={subItem.title} />
                  </ListItemButton>
                ))}
              </List>
            </Collapse>
          </React.Fragment>
        );
      }

      // Regular menu item
      return (
        <ListItem key={item.key || item.title} disablePadding>
          <ListItemButton
            onClick={() => item.action ? item.action() : handleItemClick(item.path, item.section)}
            selected={item.path ? isActive(item.path): false}
            sx={sidebarStyles.listItemButton}
          >
            <ListItemIcon>
              <Box sx={{
                ...sidebarStyles.iconContainer,
                bgcolor: item.color ? `${item.color}15` : 'transparent',
              }}>
                {React.cloneElement(item.icon, { sx: { color: item.color } })}
              </Box>
            </ListItemIcon>
            <ListItemText primary={item.title} />
          </ListItemButton>
        </ListItem>
      );
    });
  };

  const drawer = (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
        }}
      >
        <Typography variant="h6" component={Link} to="/" sx={{ textDecoration: 'none', color: 'inherit' }}>
          Poultray DZ
        </Typography>
        <IconButton onClick={() => toggleDrawer(false)}>
          <ChevronLeftIcon />
        </IconButton>
      </Box>
      <Divider />

      {user && (
        <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            {user.first_name ? user.first_name[0] : user.username ? user.username[0] : 'U'}
          </Avatar>
          <Box>
            <Typography variant="subtitle1" noWrap>
              {user.first_name && user.last_name
                ? `${user.first_name} ${user.last_name}`
                : user.username}
            </Typography>
            <Typography variant="body2" color="text.secondary" noWrap>
              {role.charAt(0).toUpperCase() + role.slice(1)}
            </Typography>
          </Box>
        </Box>
      )}

      <Divider />

      <List sx={{ flexGrow: 1 }}>
        {renderMenuItems(menuItems)}
      </List>

      <Divider />

      <List>
        <ListItem disablePadding>
          <ListItemButton
            onClick={handleLogout}
            sx={sidebarStyles.listItemButton}
          >
            <ListItemIcon>
              <Box sx={{
                ...sidebarStyles.iconContainer,
                bgcolor: '#f4433615',
              }}>
                <LogoutIcon sx={{ color: '#f44336' }} />
              </Box>
            </ListItemIcon>
            <ListItemText primary="Déconnexion" />
          </ListItemButton>
        </ListItem>
      </List>
    </>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Mobile drawer toggle button */}
      <IconButton
        color="inherit"
        aria-label="open drawer"
        edge="start"
        onClick={() => toggleDrawer(true)}
        sx={{
          position: 'fixed',
          top: 12,
          left: 10,
          zIndex: 1199,
          display: { xs: 'flex', md: 'none' },
        }}
      >
        <MenuIcon />
      </IconButton>

      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={open}
        onClose={() => toggleDrawer(false)}
        ModalProps={{ keepMounted: true }}
        sx={sidebarStyles.drawer}
      >
        {drawer}
      </Drawer>

      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            borderRight: '1px solid rgba(0, 0, 0, 0.12)',
          },
        }}
        open
      >
        {drawer}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
