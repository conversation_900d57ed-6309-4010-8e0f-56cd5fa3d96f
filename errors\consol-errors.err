XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 358ms]

XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 423ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 508ms]

XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 571ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 744ms]

XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 741ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 849ms]

XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 930ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 351ms]

XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 389ms]

XHRGET
http://localhost:5174/api/veterinaire/notifications
[HTTP/1.1 500 Internal Server Error 486ms]

Erreur lors de la récupération des données:
Object { message: "Request failed with status code 500", name: "AxiosError", code: "ERR_BAD_RESPONSE", config: {…}, request: XMLHttpRequest, response: {…}, status: 500, stack: "", … }
<anonymous code>:1:145535
XHRGET
http://localhost:5174/api/veterinaire/dashboard
[HTTP/1.1 500 Internal Server Error 324ms]

Erreur lors de la récupération des données:
Object { message: "Request failed with status code 500", name: "AxiosError", code: "ERR_BAD_RESPONSE", config: {…}, request: XMLHttpRequest, response: {…}, status: 500, stack: "", … }
<anonymous code>:1:145535
