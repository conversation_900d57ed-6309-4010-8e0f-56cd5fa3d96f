const axios = require('axios');

// Test the new API endpoints
async function testEndpoints() {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  const endpoints = [
    'http://localhost:3003/api/veterinaire/prescriptions',
    'http://localhost:3003/api/veterinaire/consultations',
    'http://localhost:3003/api/veterinaire/consultations/historique',
    'http://localhost:3003/api/veterinaire/profile'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🔄 Testing: ${endpoint}`);
      const response = await axios.get(endpoint, { headers });
      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Data:`, response.data);
    } catch (error) {
      console.log(`❌ Error: ${error.response?.status || 'Network Error'}`);
      console.log(`📝 Message:`, error.response?.data || error.message);
    }
  }
}

testEndpoints().catch(console.error);
