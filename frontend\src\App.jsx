import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, createRoutesFromElements } from 'react-router-dom';
import { UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext } from 'react-router-dom';

// Désactiver les avertissements React Router v7
UNSAFE_DataRouterContext.displayName = 'DataRouter';
UNSAFE_DataRouterStateContext.displayName = 'DataRouterState';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { MarketplaceProvider } from './contexts/MarketplaceContext';
import { theme } from './theme';

// Import components
import Login from './components/auth/Login';
import DashboardLayout from './layouts/DashboardLayout';

// Import pages
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';

// Import dashboard pages
import AdminDashboard from './pages/dashboards/AdminDashboard';
import EleveurDashboard from './pages/dashboards/EleveurDashboard';
import VeterinaireDashboard from './pages/dashboards/VeterinaireDashboard';
import MarchandDashboard from './pages/dashboards/MarchandDashboard';

// Import admin pages
import UsersManagement from './pages/admin/UsersManagement';
import AdminStatistics from './pages/admin/AdminStatistics';
import RolesPlans from './pages/admin/RolesPlans';
import SecuritySettings from './pages/admin/SecuritySettings';
import GeneralSettings from './pages/admin/GeneralSettings';
import SmtpConfig from './pages/admin/SmtpConfig';
import ApiConfig from './pages/admin/ApiConfig';
import TranslationsManager from './pages/admin/TranslationsManager';
import Blog from './pages/admin/Blog';
import AiBlogGenerator from './pages/admin/AiBlogGenerator';
import AiDataAnalysis from './pages/admin/AiDataAnalysis';
import PageContentGenerator from './pages/admin/PageContentGenerator';
import Notifications from './pages/admin/Notifications';
import Profile from './pages/admin/Profile';
import LoginAsUser from './pages/admin/LoginAsUser';

// Import other pages
import EleveursList from './pages/EleveursList';
import VolaillesList from './pages/VolaillesList';

// Import eleveur pages
import VentesEleveur from './pages/eleveur/VentesEleveur';

// Import marchand pages
import MarchandProducts from './pages/marchand/MarchandProducts';
import MarchandOrders from './pages/marchand/MarchandOrders';
import MarchandVentes from './pages/marchand/MarchandVentes';

// Import veterinaire pages
import VeterinaireConsultations from './pages/veterinaire/VeterinaireConsultations';
import PrescriptionsVeterinaire from './pages/veterinaire/PrescriptionsVeterinaire';
import VeterinaireUrgences from './pages/veterinaire/VeterinaireUrgences';
import VeterinaireHistorique from './pages/veterinaire/VeterinaireHistorique';

// Import marketplace pages
import MarketplaceSearch from './pages/marketplace/MarketplaceSearch';
import MarketplaceCart from './pages/marketplace/MarketplaceCart';
import MarketplacePayment from './pages/marketplace/MarketplacePayment';
import ProductRating from './pages/marketplace/ProductRating';

function App() {
  // Configuration des options React Router pour supprimer les avertissements
  const routerOptions = {
    future: {
      v7_startTransition: true,
      v7_relativeSplatPath: true
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <LanguageProvider>
          <MarketplaceProvider>
            <Router {...routerOptions}>
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<Home />} />
                <Route path="/login" element={<Login />} />

                {/* Protected routes with dashboard layout */}
                <Route path="/dashboard" element={<DashboardLayout />}>
                  <Route index element={<Dashboard />} />

                  {/* Admin routes */}
                  <Route path="admin" element={<AdminDashboard />} />
                  <Route path="admin/users" element={<UsersManagement />} />
                  <Route path="admin/statistics" element={<AdminStatistics />} />
                  <Route path="admin/roles" element={<RolesPlans />} />
                  <Route path="admin/security" element={<SecuritySettings />} />
                  <Route path="admin/settings" element={<GeneralSettings />} />
                  <Route path="admin/smtp" element={<SmtpConfig />} />
                  <Route path="admin/api" element={<ApiConfig />} />
                  <Route path="admin/translations" element={<TranslationsManager />} />
                  <Route path="admin/blog" element={<Blog />} />
                  <Route path="admin/ai-blog" element={<AiBlogGenerator />} />
                  <Route path="admin/ai-analysis" element={<AiDataAnalysis />} />
                  <Route path="admin/page-generator" element={<PageContentGenerator />} />
                  <Route path="admin/notifications" element={<Notifications />} />
                  <Route path="admin/profile" element={<Profile />} />
                  <Route path="admin/login-as" element={<LoginAsUser />} />

                  {/* Eleveur routes */}
                  <Route path="eleveur" element={<EleveurDashboard />} />
                  <Route path="eleveur/ventes" element={<VentesEleveur />} />

                  {/* Veterinaire routes */}
                  <Route path="veterinaire" element={<VeterinaireDashboard />} />
                  <Route path="veterinaire/consultations" element={<VeterinaireConsultations />} />
                  <Route path="veterinaire/prescriptions" element={<PrescriptionsVeterinaire />} />
                  <Route path="veterinaire/urgences" element={<VeterinaireUrgences />} />
                  <Route path="veterinaire/historique" element={<VeterinaireHistorique />} />

                  {/* Marchand routes */}
                  <Route path="marchand" element={<MarchandDashboard />} />
                  <Route path="marchand/products" element={<MarchandProducts />} />
                  <Route path="marchand/orders" element={<MarchandOrders />} />
                  <Route path="marchand/ventes" element={<MarchandVentes />} />

                  {/* General routes */}
                  <Route path="eleveurs" element={<EleveursList />} />
                  <Route path="volailles" element={<VolaillesList />} />

                  {/* Marketplace routes */}
                  <Route path="marketplace" element={<MarketplaceSearch />} />
                  <Route path="marketplace/cart" element={<MarketplaceCart />} />
                  <Route path="marketplace/payment" element={<MarketplacePayment />} />
                  <Route path="marketplace/rating" element={<ProductRating />} />
                </Route>

                {/* Redirect unknown routes to home */}
                <Route path="*" element={<Navigate to="/" replace />} />
                {/* Redirect old admin routes to new format */}
                <Route path="/admin/*" element={<Navigate to="/dashboard/admin" replace />} />
                <Route path="/eleveur/*" element={<Navigate to="/dashboard/eleveur" replace />} />
                <Route path="/veterinaire/*" element={<Navigate to="/dashboard/veterinaire" replace />} />
                <Route path="/marchand/*" element={<Navigate to="/dashboard/marchand" replace />} />
              </Routes>
            </Router>
          </MarketplaceProvider>
        </LanguageProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
