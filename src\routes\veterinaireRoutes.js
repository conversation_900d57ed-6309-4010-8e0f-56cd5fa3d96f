/**
 * Enhanced Veterinaire Dashboard Routes
 * Handles all veterinaire-related functionality including:
 * - Dashboard statistics
 * - Consultations management
 * - Prescriptions
 * - Notifications and alerts
 */

const express = require('express');
const router = express.Router();
const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const { auth } = require('../middleware/auth');
const { getProfile, updateProfile, updateDisponibilites } = require('../controllers/veterinaireController');
const { isVeterinaire } = require('../middleware/roleCheck');

// Routes pour la gestion du profil vétérinaire
router.get('/profile', auth, isVeterinaire, getProfile);
router.put('/profile', auth, isVeterinaire, updateProfile);
router.put('/disponibilites', auth, isVeterinaire, updateDisponibilites);

// @route   GET /api/veterinaire/dashboard
// @desc    Get enhanced veterinaire dashboard data
// @access  Private/Veterinaire
router.get('/dashboard', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;
    console.log('Dashboard request for veterinaire ID:', veterinaireId);

    // Check if required tables exist first
    let tablesExist = {};
    try {
      const tableCheck = await sequelize.query(
        `SELECT
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'prescriptions') as prescriptions_exists,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'consultations') as consultations_exists,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'eleveurs') as eleveurs_exists
        `,
        { type: QueryTypes.SELECT }
      );
      tablesExist = tableCheck[0];
      console.log('Tables existence check:', tablesExist);
    } catch (tableError) {
      console.error('Error checking tables:', tableError.message);
      tablesExist = { prescriptions_exists: false, consultations_exists: false, eleveurs_exists: false };
    }

    // Simplified stats query with better error handling
    const statsQuery = `
      SELECT
        COALESCE((SELECT COUNT(*) FROM prescriptions WHERE veterinaire_id = :veterinaireId), 0) as total_prescriptions,
        COALESCE((SELECT COUNT(*) FROM prescriptions WHERE veterinaire_id = :veterinaireId AND created_at >= NOW() - INTERVAL '30 days'), 0) as prescriptions_mois,
        COALESCE((SELECT COUNT(*) FROM consultations WHERE veterinaire_id = :veterinaireId), 0) as total_consultations,
        COALESCE((SELECT COUNT(*) FROM consultations WHERE veterinaire_id = :veterinaireId AND date >= NOW() - INTERVAL '30 days'), 0) as consultations_mois,
        COALESCE((SELECT COUNT(DISTINCT eleveur_id) FROM consultations WHERE veterinaire_id = :veterinaireId), 0) as eleveurs_suivis,
        COALESCE((SELECT COUNT(*) FROM consultations WHERE veterinaire_id = :veterinaireId AND date >= NOW() AND date <= NOW() + INTERVAL '7 days'), 0) as consultations_semaine_prochaine,
        COALESCE((SELECT COUNT(*) FROM prescriptions WHERE veterinaire_id = :veterinaireId AND statut IN ('en_attente', 'en_cours')), 0) as prescriptions_actives,
        0 as satisfaction_moyenne,
        0 as cout_moyen_consultation
    `;

    // Execute stats query with error handling
    let stats = {
      total_prescriptions: 0,
      prescriptions_mois: 0,
      total_consultations: 0,
      consultations_mois: 0,
      eleveurs_suivis: 0,
      consultations_semaine_prochaine: 0,
      prescriptions_actives: 0,
      satisfaction_moyenne: 0,
      cout_moyen_consultation: 0
    };

    if (tablesExist.prescriptions_exists && tablesExist.consultations_exists) {
      try {
        const statsResult = await sequelize.query(statsQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        });
        stats = statsResult[0] || stats;
        console.log('Stats query successful:', stats);
      } catch (statsError) {
        console.error('Erreur dans statsQuery:', statsError.message);
      }
    } else {
      console.log('Required tables do not exist, using default stats');
    }

    // Consultations à venir (prochaines 7 jours)
    const consultationsAVenirQuery = `
      SELECT
        c.*,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone,
        e.adresse as eleveur_adresse
      FROM consultations c
      JOIN eleveurs e ON c.eleveur_id = e.id
      WHERE c.veterinaire_id = :veterinaireId
        AND c.date >= NOW()
        AND c.date <= NOW() + INTERVAL '7 days'
        AND c.statut != 'annulee'
      ORDER BY c.date ASC
      LIMIT 10
    `;

    // Dernières prescriptions avec détails
    const prescriptionsRecentesQuery = `
      SELECT
        p.*,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone,
        v.espece,
        v.race,
        v.quantite as nombre_animaux
      FROM prescriptions p
      JOIN eleveurs e ON p.eleveur_id = e.id
      LEFT JOIN volailles v ON p.volaille_id = v.id
      WHERE p.veterinaire_id = :veterinaireId
      ORDER BY p.created_at DESC
      LIMIT 10
    `;

    // Historique des consultations récentes
    const consultationsHistoriqueQuery = `
      SELECT
        c.*,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone
      FROM consultations c
      JOIN eleveurs e ON c.eleveur_id = e.id
      WHERE c.veterinaire_id = :veterinaireId
        AND c.date <= NOW()
        AND c.statut = 'terminee'
      ORDER BY c.date DESC
      LIMIT 10
    `;

    // Graphiques - Consultations par mois (6 derniers mois)
    const consultationsGraphiqueQuery = `
      SELECT
        DATE_TRUNC('month', c.date) as mois,
        COUNT(*) as nombre_consultations,
        COUNT(*) FILTER (WHERE c.statut = 'terminee') as consultations_terminees,
        AVG(CASE WHEN c.note_satisfaction IS NOT NULL THEN c.note_satisfaction END) as satisfaction_moyenne
      FROM consultations c
      WHERE c.veterinaire_id = :veterinaireId
        AND c.date >= NOW() - INTERVAL '6 months'
      GROUP BY DATE_TRUNC('month', c.date)
      ORDER BY mois DESC
    `;

    // Types de consultations les plus fréquents
    const typesConsultationsQuery = `
      SELECT
        COALESCE(c.symptomes, 'Non spécifié') as motif,
        COUNT(*) as nombre,
        AVG(CASE WHEN c.note_satisfaction IS NOT NULL THEN c.note_satisfaction END) as satisfaction_moyenne
      FROM consultations c
      WHERE c.veterinaire_id = :veterinaireId
        AND c.date >= NOW() - INTERVAL '6 months'
        AND c.statut = 'terminee'
      GROUP BY c.symptomes
      ORDER BY nombre DESC
      LIMIT 5
    `;

    // Alertes santé récentes - simplified query to avoid complex joins
    const alertesSanteQuery = `
      SELECT
        c.id,
        c.date,
        CONCAT('Consultation urgente requise pour ', COALESCE(e.nom, ''), ' ', COALESCE(e.prenom, '')) as titre,
        c.symptomes as message,
        'high' as priorite,
        c.created_at,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone
      FROM consultations c
      LEFT JOIN eleveurs e ON c.eleveur_id = e.id
      WHERE c.veterinaire_id = :veterinaireId
        AND c.urgence = true
        AND c.statut IN ('programmee', 'en_cours')
        AND c.date >= NOW() - INTERVAL '7 days'
      ORDER BY c.date DESC
      LIMIT 5
    `;

    // Initialize empty arrays for all data
    let consultationsAVenir = [];
    let prescriptionsRecentes = [];
    let consultationsHistorique = [];
    let consultationsGraphique = [];
    let typesConsultations = [];
    let alertesSante = [];

    // Only execute queries if tables exist
    if (tablesExist.consultations_exists && tablesExist.eleveurs_exists) {
      try {
        // Execute queries with individual error handling
        const queryPromises = [];

        queryPromises.push(
          sequelize.query(consultationsAVenirQuery, {
            replacements: { veterinaireId },
            type: QueryTypes.SELECT
          }).catch(err => {
            console.error('Erreur dans consultationsAVenirQuery:', err.message);
            return [];
          })
        );

        if (tablesExist.prescriptions_exists) {
          queryPromises.push(
            sequelize.query(prescriptionsRecentesQuery, {
              replacements: { veterinaireId },
              type: QueryTypes.SELECT
            }).catch(err => {
              console.error('Erreur dans prescriptionsRecentesQuery:', err.message);
              return [];
            })
          );
        } else {
          queryPromises.push(Promise.resolve([]));
        }

        queryPromises.push(
          sequelize.query(consultationsHistoriqueQuery, {
            replacements: { veterinaireId },
            type: QueryTypes.SELECT
          }).catch(err => {
            console.error('Erreur dans consultationsHistoriqueQuery:', err.message);
            return [];
          })
        );

        queryPromises.push(
          sequelize.query(consultationsGraphiqueQuery, {
            replacements: { veterinaireId },
            type: QueryTypes.SELECT
          }).catch(err => {
            console.error('Erreur dans consultationsGraphiqueQuery:', err.message);
            return [];
          })
        );

        queryPromises.push(
          sequelize.query(typesConsultationsQuery, {
            replacements: { veterinaireId },
            type: QueryTypes.SELECT
          }).catch(err => {
            console.error('Erreur dans typesConsultationsQuery:', err.message);
            return [];
          })
        );

        queryPromises.push(
          sequelize.query(alertesSanteQuery, {
            replacements: { veterinaireId },
            type: QueryTypes.SELECT
          }).catch(err => {
            console.error('Erreur dans alertesSanteQuery:', err.message);
            return [];
          })
        );

        const results = await Promise.all(queryPromises);
        [consultationsAVenir, prescriptionsRecentes, consultationsHistorique, consultationsGraphique, typesConsultations, alertesSante] = results;

        console.log('All queries executed successfully');
      } catch (error) {
        console.error('Error executing dashboard queries:', error.message);
      }
    } else {
      console.log('Required tables missing, returning empty data');
    }

    // Return dashboard data
    res.json({
      status: 'success',
      data: {
        stats: {
          totalPrescriptions: parseInt(stats.total_prescriptions) || 0,
          prescriptionsMois: parseInt(stats.prescriptions_mois) || 0,
          totalConsultations: parseInt(stats.total_consultations) || 0,
          consultationsMois: parseInt(stats.consultations_mois) || 0,
          eleveursSuivis: parseInt(stats.eleveurs_suivis) || 0,
          consultationsSemaineProchaine: parseInt(stats.consultations_semaine_prochaine) || 0,
          prescriptionsActives: parseInt(stats.prescriptions_actives) || 0,
          satisfactionMoyenne: parseFloat(stats.satisfaction_moyenne) || 0,
          coutMoyenConsultation: parseFloat(stats.cout_moyen_consultation) || 0
        },
        consultationsAVenir,
        prescriptionsRecentes,
        consultationsHistorique,
        graphiques: {
          consultationsParMois: consultationsGraphique,
          typesConsultations
        },
        alertesSante
      }
    });
  } catch (error) {
    console.error('Erreur générale dans le dashboard:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors du chargement du dashboard',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/veterinaire/notifications
// @desc    Get veterinaire notifications and alerts
// @access  Private/Veterinaire
router.get('/notifications', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;
    console.log('Notifications request for veterinaire ID:', veterinaireId);

    // Check if required tables exist first
    let tablesExist = {};
    try {
      const tableCheck = await sequelize.query(
        `SELECT
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'consultations') as consultations_exists,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'prescriptions') as prescriptions_exists,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'eleveurs') as eleveurs_exists
        `,
        { type: QueryTypes.SELECT }
      );
      tablesExist = tableCheck[0];
      console.log('Tables existence check for notifications:', tablesExist);
    } catch (tableError) {
      console.error('Error checking tables for notifications:', tableError.message);
      tablesExist = { consultations_exists: false, prescriptions_exists: false, eleveurs_exists: false };
    }

    let notifications = [];

    // Only execute queries if tables exist
    if (tablesExist.consultations_exists && tablesExist.eleveurs_exists) {
      try {
        // Simplified notifications query with better error handling
        const consultationsNotificationsQuery = `
          SELECT
            'consultation' as type,
            c.id,
            c.date,
            CONCAT('Consultation programmée avec ', COALESCE(e.nom, ''), ' ', COALESCE(e.prenom, '')) as message,
            COALESCE(c.symptomes, 'Aucun symptôme spécifié') as details,
            'info' as priorite,
            c.created_at
          FROM consultations c
          LEFT JOIN eleveurs e ON c.eleveur_id = e.id
          WHERE c.veterinaire_id = :veterinaireId
            AND c.date >= NOW()
            AND c.date <= NOW() + INTERVAL '24 hours'
            AND c.statut = 'programmee'
          ORDER BY c.date DESC
          LIMIT 10
        `;

        const consultationsNotifications = await sequelize.query(consultationsNotificationsQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        }).catch(err => {
          console.error('Error in consultations notifications query:', err.message);
          return [];
        });

        notifications = [...notifications, ...consultationsNotifications];

        // Add prescriptions notifications if table exists
        if (tablesExist.prescriptions_exists) {
          const prescriptionsNotificationsQuery = `
            SELECT
              'prescription' as type,
              p.id,
              p.created_at as date,
              CONCAT('Prescription en attente pour ', COALESCE(e.nom, ''), ' ', COALESCE(e.prenom, '')) as message,
              COALESCE(p.diagnostic, 'Diagnostic non spécifié') as details,
              'warning' as priorite,
              p.created_at
            FROM prescriptions p
            LEFT JOIN eleveurs e ON p.eleveur_id = e.id
            WHERE p.veterinaire_id = :veterinaireId
              AND p.statut IN ('en_attente', 'en_cours')
              AND p.created_at >= NOW() - INTERVAL '7 days'
            ORDER BY p.created_at DESC
            LIMIT 10
          `;

          const prescriptionsNotifications = await sequelize.query(prescriptionsNotificationsQuery, {
            replacements: { veterinaireId },
            type: QueryTypes.SELECT
          }).catch(err => {
            console.error('Error in prescriptions notifications query:', err.message);
            return [];
          });

          notifications = [...notifications, ...prescriptionsNotifications];
        }

        // Sort notifications by date
        notifications.sort((a, b) => new Date(b.date) - new Date(a.date));
        notifications = notifications.slice(0, 20); // Limit to 20 notifications

        console.log('Notifications query successful, found:', notifications.length);
      } catch (error) {
        console.error('Error executing notifications queries:', error.message);
        notifications = [];
      }
    } else {
      console.log('Required tables missing for notifications, returning empty array');
      // Return a default notification when tables don't exist
      notifications = [{
        type: 'info',
        id: 1,
        date: new Date(),
        message: 'Bienvenue dans votre espace vétérinaire',
        details: 'Votre tableau de bord est prêt à être utilisé',
        priorite: 'info',
        created_at: new Date()
      }];
    }

    res.json({
      status: 'success',
      data: {
        notifications: notifications
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la récupération des notifications',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/veterinaire/consultations/quick
// @desc    Quick action - Schedule new consultation
// @access  Private/Veterinaire
router.post('/consultations/quick', auth, isVeterinaire, async (req, res) => {
  try {
    const {
      eleveur_id,
      date_consultation,
      motif,
      urgence = false
    } = req.body;

    const veterinaireId = req.user.id;

    const insertQuery = `
      INSERT INTO consultations (
        veterinaire_id, eleveur_id, date,
        symptomes, statut, urgence, created_at, updated_at
      ) VALUES (:veterinaireId, :eleveur_id, :date_consultation, :motif, 'programmee', :urgence, NOW(), NOW())
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: { veterinaireId, eleveur_id, date_consultation, motif, urgence },
      type: QueryTypes.INSERT
    });

    res.status(201).json({
      status: 'success',
      message: 'Consultation programmée avec succès',
      data: {
        consultation: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la programmation rapide de consultation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la programmation de consultation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/veterinaire/prescriptions/quick
// @desc    Quick action - Create new prescription
// @access  Private/Veterinaire
router.post('/prescriptions/quick', auth, isVeterinaire, async (req, res) => {
  try {
    const {
      eleveur_id,
      volaille_id,
      medicament,
      dosage,
      duree_traitement,
      instructions
    } = req.body;

    const veterinaireId = req.user.id;

    // Générer un numéro de prescription unique
    const datePart = new Date().toISOString().slice(0,10).replace(/-/g, '');
    const randomPart = Math.floor(10000 + Math.random() * 90000);
    const numero_prescription = `RX-${datePart}-${randomPart}`;

    const insertQuery = `
      INSERT INTO prescriptions (
        veterinaire_id, eleveur_id, volaille_id,
        numero_prescription, diagnostic, posologie,
        duree_traitement, medicaments, statut, created_at, updated_at
      ) VALUES (
        :veterinaireId, :eleveur_id, :volaille_id,
        :numero_prescription, :instructions, :dosage,
        :duree_traitement, :medicaments, 'en_attente', NOW(), NOW()
      )
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: {
        veterinaireId,
        eleveur_id,
        volaille_id,
        numero_prescription,
        instructions,
        dosage,
        duree_traitement,
        medicaments: JSON.stringify([{ nom: medicament, dosage, instructions }])
      },
      type: QueryTypes.INSERT
    });

    res.status(201).json({
      status: 'success',
      message: 'Prescription créée avec succès',
      data: {
        prescription: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la création rapide de prescription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la création de prescription',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
