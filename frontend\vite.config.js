import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [
    react({
      jsxRuntime: 'automatic',
      jsxImportSource: 'react',
      include: "**/*.{jsx,tsx}",
      fastRefresh: true
    })
  ],
  define: {
    'process.env': {}
  },
  build: {
    sourcemap: true,
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true,
    rollupOptions: {
      external: [
        '@firebase/auth',
        '@firebase/app',
        '@firebase/firestore',
        '@firebase/storage'
      ]
    }
  },
  css: {
    devSourcemap: true
  },
  server: {
    port: 5174,
    host: true,
    strictPort: false,
    hmr: true,
    cors: true,
    fs: {
      strict: false,
      allow: ['..']
    },
    proxy: {
      '/api': {
        target: 'http://localhost:3003',
        changeOrigin: true,
        secure: false,
        ws: true
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
    extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json', '.map'],
    preserveSymlinks: true
  },
  optimizeDeps: {
    include: ['react', 'react-dom', '@mui/material', '@mui/icons-material', 'recharts'],
    exclude: []
  }
})
