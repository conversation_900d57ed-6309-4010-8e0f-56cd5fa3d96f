const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'poultraydz',
  password: process.env.DB_PASSWORD || 'root',
  port: process.env.DB_PORT || 5432,
});

// Récupérer le panier de l'utilisateur
router.get('/cart', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Récupérer les articles du panier avec les détails des produits
    const cartQuery = await client.query(`
      SELECT c.id as cart_item_id, c.quantity,
             p.id as produit_id, p.nom, p.description, p.prix, p.image_url,
             p.stock_disponible, p.categorie,
             (p.prix * c.quantity) as sous_total
      FROM panier c
      JOIN produits p ON c.produit_id = p.id
      WHERE c.user_id = $1 AND c.status = 'actif'
      ORDER BY c.created_at DESC
    `, [req.user.id]);

    // Calculer le total du panier
    const totalQuery = await client.query(`
      SELECT SUM(p.prix * c.quantity) as total
      FROM panier c
      JOIN produits p ON c.produit_id = p.id
      WHERE c.user_id = $1 AND c.status = 'actif'
    `, [req.user.id]);

    await client.query('COMMIT');

    res.json({
      success: true,
      data: {
        items: cartQuery.rows,
        total: totalQuery.rows[0].total || 0
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la récupération du panier:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du panier',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Mettre à jour la quantité d'un produit dans le panier
router.put('/cart/:itemId', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const { itemId } = req.params;
    const { quantity } = req.body;

    // Vérifier que l'article appartient à l'utilisateur
    const cartItem = await client.query(
      'SELECT produit_id FROM panier WHERE id = $1 AND user_id = $2',
      [itemId, req.user.id]
    );

    if (cartItem.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Article non trouvé dans le panier'
      });
    }

    // Vérifier le stock disponible
    const stockQuery = await client.query(
      'SELECT stock_disponible FROM produits WHERE id = $1',
      [cartItem.rows[0].produit_id]
    );

    if (stockQuery.rows[0].stock_disponible < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Stock insuffisant'
      });
    }

    // Mettre à jour la quantité
    await client.query(`
      UPDATE panier
      SET quantity = $1, updated_at = NOW()
      WHERE id = $2 AND user_id = $3
    `, [quantity, itemId, req.user.id]);

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'Quantité mise à jour avec succès'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la mise à jour de la quantité:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la quantité',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Supprimer un produit du panier
router.delete('/cart/:itemId', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  try {
    const { itemId } = req.params;

    // Supprimer l'article (soft delete)
    const result = await client.query(`
      UPDATE panier
      SET status = 'supprime', updated_at = NOW()
      WHERE id = $1 AND user_id = $2
      RETURNING id
    `, [itemId, req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Article non trouvé dans le panier'
      });
    }

    res.json({
      success: true,
      message: 'Article supprimé du panier avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'article:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'article',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Récupérer les favoris de l'utilisateur
router.get('/favorites', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  try {
    // Récupérer les produits favoris avec leurs détails
    const result = await client.query(`
      SELECT f.id as favori_id, f.created_at as date_ajout,
             p.id as produit_id, p.nom, p.description, p.prix,
             p.image_url, p.stock_disponible, p.categorie
      FROM favoris f
      JOIN produits p ON f.produit_id = p.id
      WHERE f.user_id = $1 AND f.status = 'actif'
      ORDER BY f.created_at DESC
    `, [req.user.id]);

    res.json({
      success: true,
      data: {
        favorites: result.rows
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des favoris:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des favoris',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Ajouter un produit aux favoris
router.post('/favorites', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const { produit_id } = req.body;

    // Vérifier si le produit existe
    const produitQuery = await client.query(
      'SELECT id FROM produits WHERE id = $1',
      [produit_id]
    );

    if (produitQuery.rows.length === 0) {
      throw new Error('Produit non trouvé');
    }

    // Vérifier si le produit est déjà en favori
    const existingFavori = await client.query(
      'SELECT id FROM favoris WHERE user_id = $1 AND produit_id = $2 AND status = \'actif\'',
      [req.user.id, produit_id]
    );

    if (existingFavori.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Ce produit est déjà dans vos favoris'
      });
    }

    // Ajouter aux favoris
    const result = await client.query(`
      INSERT INTO favoris (user_id, produit_id, status)
      VALUES ($1, $2, 'actif')
      RETURNING id
    `, [req.user.id, produit_id]);

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'Produit ajouté aux favoris avec succès',
      data: { id: result.rows[0].id }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de l\'ajout aux favoris:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout aux favoris',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Supprimer un produit des favoris
router.delete('/favorites/:favoriId', authenticateToken, async (req, res) => {
  const client = await pool.connect();
  try {
    const { favoriId } = req.params;

    // Supprimer le favori (soft delete)
    const result = await client.query(`
      UPDATE favoris
      SET status = 'supprime', updated_at = NOW()
      WHERE id = $1 AND user_id = $2
      RETURNING id
    `, [favoriId, req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Favori non trouvé'
      });
    }

    res.json({
      success: true,
      message: 'Produit retiré des favoris avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du favori:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du favori',
      error: error.message
    });
  } finally {
    client.release();
  }
});

module.exports = router;