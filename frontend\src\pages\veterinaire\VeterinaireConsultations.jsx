import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent
} from '@mui/material';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Line } from 'react-chartjs-2';
import jsPDF from 'jspdf';
import 'chart.js/auto';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';

const VeterinaireConsultations = () => {
  const { user } = useAuth();
  const [consultations, setConsultations] = useState([]);
  const [selectedConsultation, setSelectedConsultation] = useState(null);
  const [diagnostic, setDiagnostic] = useState({
    symptomes: '',
    diagnostic: '',
    traitement: '',
    notes: ''
  });
  const [stats, setStats] = useState({
    labels: [],
    datasets: [{
      label: 'Consultations par jour',
      data: [],
      borderColor: 'rgb(75, 192, 192)',
      tension: 0.1
    }]
  });

  useEffect(() => {
    fetchConsultations();
    fetchStats();
  }, []);

  const fetchConsultations = async () => {
    try {
      const response = await axios.get('/api/veterinaire/consultations');
      const formattedConsultations = response.data.map(consultation => ({
        id: consultation.id,
        title: `Consultation - ${consultation.eleveur.nom}`,
        start: consultation.date_consultation,
        extendedProps: consultation
      }));
      setConsultations(formattedConsultations);
    } catch (error) {
      console.error('Erreur lors du chargement des consultations:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/veterinaire/stats');
      setStats({
        labels: response.data.dates,
        datasets: [{
          label: 'Consultations par jour',
          data: response.data.counts,
          borderColor: 'rgb(75, 192, 192)',
          tension: 0.1
        }]
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  const handleEventClick = (clickInfo) => {
    setSelectedConsultation(clickInfo.event.extendedProps);
    setDiagnostic({
      symptomes: clickInfo.event.extendedProps.symptomes || '',
      diagnostic: clickInfo.event.extendedProps.diagnostic || '',
      traitement: clickInfo.event.extendedProps.traitement || '',
      notes: clickInfo.event.extendedProps.notes || ''
    });
  };

  const handleDiagnosticSubmit = async () => {
    try {
      await axios.put(`/api/veterinaire/consultations/${selectedConsultation.id}`, diagnostic);
      fetchConsultations();
      alert('Diagnostic enregistré avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du diagnostic:', error);
    }
  };

  const generatePDF = () => {
    const doc = new jsPDF();

    // En-tête
    doc.setFontSize(18);
    doc.text('Ordonnance Médicale', 105, 20, { align: 'center' });

    // Informations du vétérinaire
    doc.setFontSize(12);
    doc.text(`Dr. ${user.nom} ${user.prenom}`, 20, 40);
    doc.text(`N° Ordre: ${user.numero_ordre}`, 20, 50);

    // Informations de la consultation
    doc.text('Diagnostic:', 20, 70);
    doc.setFontSize(10);
    doc.text(diagnostic.diagnostic, 30, 80);

    doc.setFontSize(12);
    doc.text('Traitement prescrit:', 20, 100);
    doc.setFontSize(10);
    doc.text(diagnostic.traitement, 30, 110);

    // Date et signature
    doc.setFontSize(10);
    doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 180);
    doc.text('Signature:', 120, 180);

    doc.save('ordonnance.pdf');
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Grid container spacing={3}>
        {/* Calendrier */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="timeGridWeek"
              headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
              }}
              events={consultations}
              eventClick={handleEventClick}
              locale="fr"
            />
          </Paper>
        </Grid>

        {/* Formulaire de diagnostic */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Diagnostic et Ordonnance
            </Typography>
            {selectedConsultation ? (
              <Box component="form">
                <TextField
                  fullWidth
                  label="Symptômes"
                  multiline
                  rows={2}
                  value={diagnostic.symptomes}
                  onChange={(e) => setDiagnostic({ ...diagnostic, symptomes: e.target.value })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="Diagnostic"
                  multiline
                  rows={2}
                  value={diagnostic.diagnostic}
                  onChange={(e) => setDiagnostic({ ...diagnostic, diagnostic: e.target.value })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="Traitement"
                  multiline
                  rows={2}
                  value={diagnostic.traitement}
                  onChange={(e) => setDiagnostic({ ...diagnostic, traitement: e.target.value })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={2}
                  value={diagnostic.notes}
                  onChange={(e) => setDiagnostic({ ...diagnostic, notes: e.target.value })}
                  margin="normal"
                />
                <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleDiagnosticSubmit}
                  >
                    Enregistrer
                  </Button>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={generatePDF}
                  >
                    Générer PDF
                  </Button>
                </Box>
              </Box>
            ) : (
              <Typography color="textSecondary">
                Sélectionnez une consultation dans le calendrier
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Statistiques */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Statistiques des Consultations
            </Typography>
            <Box sx={{ height: 300 }}>
              <Line data={stats} options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    ticks: {
                      stepSize: 1
                    }
                  }
                }
              }} />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VeterinaireConsultations;
