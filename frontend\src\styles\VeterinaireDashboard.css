/* Enhanced Veterinary Dashboard Styles */
/* Clinical Blue/Green Color Scheme with Urgent Red Highlights */

:root {
  /* Clinical Color Palette */
  --clinical-blue-primary: #1976d2;
  --clinical-blue-light: #e3f2fd;
  --clinical-blue-dark: #0d47a1;
  
  --clinical-green-primary: #2e7d32;
  --clinical-green-light: #e8f5e8;
  --clinical-green-dark: #1b5e20;
  
  --urgent-red: #ff4444;
  --urgent-red-light: #ffebee;
  --urgent-red-dark: #c62828;
  
  --warning-orange: #ff8800;
  --warning-orange-light: #fff3e0;
  
  --success-green: #00C49F;
  --info-blue: #0088FE;
  --neutral-gray: #757575;
  
  /* Tablet-friendly spacing */
  --mobile-padding: 12px;
  --tablet-padding: 16px;
  --desktop-padding: 24px;
}

/* Veterinary Dashboard Container */
.veterinary-dashboard {
  background: linear-gradient(135deg, var(--clinical-blue-light) 0%, #ffffff 100%);
  min-height: 100vh;
  font-family: 'Roboto', 'Arial', sans-serif;
}

/* Enhanced Cards with Clinical Theme */
.vet-card {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15) !important;
  transition: all 0.3s ease !important;
  border: 1px solid rgba(25, 118, 210, 0.1) !important;
}

.vet-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(25, 118, 210, 0.25) !important;
}

/* Urgent Alert Cards */
.urgent-alert-card {
  border-left: 4px solid var(--urgent-red) !important;
  background: linear-gradient(90deg, var(--urgent-red-light) 0%, #ffffff 100%) !important;
}

.urgent-alert-card .MuiTypography-h6 {
  color: var(--urgent-red) !important;
  font-weight: 600 !important;
}

/* Active Patient Cards */
.patient-card {
  border-left: 4px solid var(--clinical-green-primary) !important;
  background: linear-gradient(90deg, var(--clinical-green-light) 0%, #ffffff 100%) !important;
}

/* Real-time Appointment Tracker */
.appointment-tracker {
  border-left: 4px solid var(--clinical-blue-primary) !important;
  background: linear-gradient(90deg, var(--clinical-blue-light) 0%, #ffffff 100%) !important;
}

.current-appointment {
  background: var(--clinical-blue-light) !important;
  border-radius: 8px !important;
  padding: 16px !important;
  border: 2px solid var(--clinical-blue-primary) !important;
}

/* Medication Inventory */
.medication-inventory {
  border-left: 4px solid var(--warning-orange) !important;
}

.stock-critical {
  color: var(--urgent-red) !important;
  font-weight: 600 !important;
}

.stock-low {
  color: var(--warning-orange) !important;
  font-weight: 500 !important;
}

.stock-ok {
  color: var(--clinical-green-primary) !important;
  font-weight: 500 !important;
}

/* Interactive Charts */
.interactive-chart {
  background: #ffffff !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.chart-filters {
  background: var(--clinical-blue-light) !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  margin-bottom: 16px !important;
}

/* Quick Action Buttons */
.quick-action-btn {
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
}

.quick-action-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
}

.quick-action-primary {
  background: linear-gradient(135deg, var(--clinical-blue-primary) 0%, var(--clinical-blue-dark) 100%) !important;
}

.quick-action-success {
  background: linear-gradient(135deg, var(--clinical-green-primary) 0%, var(--clinical-green-dark) 100%) !important;
}

.quick-action-urgent {
  background: linear-gradient(135deg, var(--urgent-red) 0%, var(--urgent-red-dark) 100%) !important;
}

/* Status Chips */
.status-chip-urgent {
  background: var(--urgent-red) !important;
  color: white !important;
  font-weight: 600 !important;
}

.status-chip-stable {
  background: var(--clinical-green-primary) !important;
  color: white !important;
  font-weight: 500 !important;
}

.status-chip-post-op {
  background: var(--warning-orange) !important;
  color: white !important;
  font-weight: 500 !important;
}

/* Progress Bars */
.progress-bar-critical {
  background: var(--urgent-red) !important;
}

.progress-bar-low {
  background: var(--warning-orange) !important;
}

.progress-bar-ok {
  background: var(--clinical-green-primary) !important;
}

/* Responsive Design - Tablet Friendly */
@media (max-width: 1024px) {
  .veterinary-dashboard {
    padding: var(--tablet-padding) !important;
  }
  
  .vet-card {
    margin-bottom: 16px !important;
  }
  
  .quick-action-btn {
    padding: 10px 20px !important;
    font-size: 0.875rem !important;
  }
  
  /* Stack charts vertically on tablets */
  .chart-container {
    margin-bottom: 24px !important;
  }
  
  /* Increase touch targets for tablets */
  .MuiIconButton-root {
    padding: 12px !important;
  }
  
  .MuiButton-root {
    min-height: 44px !important;
  }
}

@media (max-width: 768px) {
  .veterinary-dashboard {
    padding: var(--mobile-padding) !important;
  }
  
  /* Mobile-specific adjustments */
  .vet-card {
    margin-bottom: 12px !important;
    padding: 12px !important;
  }
  
  .quick-action-btn {
    width: 100% !important;
    margin-bottom: 8px !important;
  }
  
  /* Compact table for mobile */
  .MuiTableCell-root {
    padding: 8px !important;
    font-size: 0.75rem !important;
  }
  
  /* Hide less important columns on mobile */
  .mobile-hide {
    display: none !important;
  }
}

/* High Data Density Layout */
.high-density-table {
  font-size: 0.875rem !important;
}

.high-density-table .MuiTableCell-root {
  padding: 6px 8px !important;
  border-bottom: 1px solid rgba(224, 224, 224, 0.5) !important;
}

.high-density-table .MuiTableHead-root {
  background: var(--clinical-blue-light) !important;
}

.high-density-table .MuiTableHead-root .MuiTableCell-root {
  font-weight: 600 !important;
  color: var(--clinical-blue-dark) !important;
}

/* Compact List Items */
.compact-list-item {
  padding: 8px 16px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.compact-list-item:hover {
  background: var(--clinical-blue-light) !important;
}

/* Enhanced Accordion for Patient Details */
.patient-accordion {
  border: 1px solid rgba(25, 118, 210, 0.2) !important;
  border-radius: 8px !important;
  margin-bottom: 8px !important;
}

.patient-accordion:before {
  display: none !important;
}

.patient-accordion-summary {
  background: var(--clinical-blue-light) !important;
  border-radius: 8px 8px 0 0 !important;
}

.patient-accordion-details {
  background: #ffffff !important;
  border-top: 1px solid rgba(25, 118, 210, 0.1) !important;
}

/* Floating Action Button */
.vet-fab {
  background: linear-gradient(135deg, var(--clinical-blue-primary) 0%, var(--clinical-blue-dark) 100%) !important;
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.4) !important;
}

.vet-fab:hover {
  background: linear-gradient(135deg, var(--clinical-blue-dark) 0%, var(--clinical-blue-primary) 100%) !important;
  transform: scale(1.05) !important;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
  background-size: 200% 100% !important;
  animation: loading 1.5s infinite !important;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Print Styles for Reports */
@media print {
  .no-print {
    display: none !important;
  }
  
  .vet-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
    break-inside: avoid !important;
  }
  
  .veterinary-dashboard {
    background: white !important;
  }
}

/* Accessibility Enhancements */
.high-contrast {
  filter: contrast(1.2) !important;
}

.focus-visible {
  outline: 2px solid var(--clinical-blue-primary) !important;
  outline-offset: 2px !important;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out !important;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--clinical-blue-light);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--clinical-blue-primary);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--clinical-blue-dark);
}
