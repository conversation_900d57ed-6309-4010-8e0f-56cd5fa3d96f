import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  Card,
  CardContent,
  IconButton,
  Alert
} from '@mui/material';
import {
  Warning as WarningIcon,
  LocationOn as LocationIcon,
  Message as MessageIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import axios from 'axios';

const VeterinaireUrgences = () => {
  const [urgences, setUrgences] = useState([]);
  const [eleveurs, setEleveurs] = useState([]);
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    // Chargement initial des données
    fetchUrgences();
    fetchEleveurs();
  }, []);

  const fetchUrgences = async () => {
    try {
      const response = await axios.get('/api/veterinaire/urgences');
      setUrgences(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des urgences:', error);
    }
  };

  const fetchEleveurs = async () => {
    try {
      const response = await axios.get('/api/veterinaire/eleveurs');
      setEleveurs(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des éleveurs:', error);
    }
  };

  const handleUrgenceResponse = async (urgenceId, response) => {
    try {
      await axios.put(`/api/veterinaire/urgences/${urgenceId}`, { response });
      fetchUrgences();
    } catch (error) {
      console.error('Erreur lors de la réponse à l\'urgence:', error);
    }
  };

  const sendMessage = (eleveurId, message) => {
    // TODO: Implement real-time messaging
    console.log(`Sending message to eleveur ${eleveurId}: ${message}`);
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'haute':
        return 'error';
      case 'moyenne':
        return 'warning';
      default:
        return 'info';
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Grid container spacing={3}>
        {/* Liste des urgences */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Urgences en cours
            </Typography>
            <List>
              {urgences.map((urgence) => (
                <ListItem
                  key={urgence.id}
                  sx={{
                    mb: 1,
                    border: 1,
                    borderColor: 'grey.300',
                    borderRadius: 1
                  }}
                >
                  <ListItemIcon>
                    <WarningIcon color={getPriorityColor(urgence.priorite)} />
                  </ListItemIcon>
                  <ListItemText
                    primary={urgence.titre}
                    secondary={
                      <>
                        <Typography variant="body2">{urgence.description}</Typography>
                        <Chip
                          size="small"
                          label={urgence.priorite}
                          color={getPriorityColor(urgence.priorite)}
                          sx={{ mt: 1 }}
                        />
                      </>
                    }
                  />
                  <Box sx={{ ml: 2 }}>
                    <IconButton
                      color="primary"
                      onClick={() => sendMessage(urgence.eleveurId, 'En route')}
                    >
                      <MessageIcon />
                    </IconButton>
                    <IconButton
                      color="secondary"
                      href={`tel:${urgence.eleveur?.telephone}`}
                    >
                      <PhoneIcon />
                    </IconButton>
                  </Box>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Liste des éleveurs */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Éleveurs disponibles
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Carte interactive en cours de développement. Fonctionnalité temporairement désactivée.
            </Alert>
            <Grid container spacing={2}>
              {eleveurs.map((eleveur) => (
                <Grid item xs={12} sm={6} md={4} key={eleveur.id}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {eleveur.nom}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {eleveur.adresse}
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        <Button
                          size="small"
                          startIcon={<MessageIcon />}
                          onClick={() => sendMessage(eleveur.id, 'Bonjour, puis-je vous aider ?')}
                          sx={{ mr: 1 }}
                        >
                          Contacter
                        </Button>
                        <IconButton
                          color="secondary"
                          href={`tel:${eleveur.telephone}`}
                        >
                          <PhoneIcon />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Chat en temps réel */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Messages récents
            </Typography>
            <List>
              {messages.map((message, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={message.expediteur}
                    secondary={message.contenu}
                  />
                  <Typography variant="caption" color="textSecondary">
                    {new Date(message.date).toLocaleTimeString()}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VeterinaireUrgences;
