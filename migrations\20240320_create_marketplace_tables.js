const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'poultraydz',
  password: process.env.DB_PASSWORD || 'root',
  port: process.env.DB_PORT || 5432,
});

async function createMarketplaceTables() {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Table des produits
    await client.query(`
      CREATE TABLE IF NOT EXISTS produits (
        id SERIAL PRIMARY KEY,
        nom VARCHAR(255) NOT NULL,
        description TEXT,
        prix DECIMAL(10,2) NOT NULL,
        stock INTEGER NOT NULL DEFAULT 0,
        image_url TEXT,
        categorie VARCHAR(50),
        marchand_id INTEGER REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table du panier
    await client.query(`
      CREATE TABLE IF NOT EXISTS panier (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        produit_id INTEGER REFERENCES produits(id),
        quantity INTEGER NOT NULL DEFAULT 1,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, produit_id)
      )
    `);

    // Table des favoris
    await client.query(`
      CREATE TABLE IF NOT EXISTS favoris (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        produit_id INTEGER REFERENCES produits(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, produit_id)
      )
    `);

    // Table des commandes
    await client.query(`
      CREATE TABLE IF NOT EXISTS commandes (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        statut VARCHAR(50) NOT NULL DEFAULT 'en_attente',
        montant_total DECIMAL(10,2) NOT NULL,
        frais_livraison DECIMAL(10,2) NOT NULL DEFAULT 500,
        adresse_livraison TEXT NOT NULL,
        telephone VARCHAR(20) NOT NULL,
        instructions_livraison TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des détails de commande
    await client.query(`
      CREATE TABLE IF NOT EXISTS commande_details (
        id SERIAL PRIMARY KEY,
        commande_id INTEGER REFERENCES commandes(id),
        produit_id INTEGER REFERENCES produits(id),
        quantity INTEGER NOT NULL,
        prix_unitaire DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Trigger pour mettre à jour updated_at
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    // Appliquer le trigger aux tables concernées
    const tables = ['produits', 'panier', 'commandes'];
    for (const table of tables) {
      await client.query(`
        DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};
        CREATE TRIGGER update_${table}_updated_at
        BEFORE UPDATE ON ${table}
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `);
    }

    await client.query('COMMIT');
    console.log('Tables du marketplace créées avec succès');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Erreur lors de la création des tables du marketplace:', error);
    throw error;
  } finally {
    client.release();
  }
}

createMarketplaceTables().catch(console.error);